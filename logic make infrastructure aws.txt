I want to generate code terraform for:
 - 1 VPC:
	Name: baiker-dev-vpc
	IPv4: 10.0.0.0/16
	IPv6: No IPv6 CIDR block
	Main network ACL: true
	Main route table: true
	Number of Availability Zones (AZs): 2
	Number of public subnets: 2
	Number of private subnets: 4
	NAT Gateway: in 1 AZ
	VPC Endpoint: None
	DNS options:
		- Enable DNS hostname: yes
		- Enable DNS resolution: yes

 - 2 Security Group:
		* Security Group 1:
			Name: baiker-dev-sg
			Desciption: Allow Port to EC2 using GPU
			VPC: output name of baiker-dev-vpc
			Inbound:
				Rule 1:
					- IP: IPv4
					- Type: All SSH
					- Protocol: TCP
					- Port range: 22
					- Source: output security groups id of baiker-dev-sg-ssh-bastion
				Rule 2:
					- IP: IPv4
					- Type: Custom TCP
					- Protocol: TCP
					- Port range: 9999
					- Source: 0.0.0.0/0
				Rule 3:
					- IP: IPv4
					- Type: Custom TCP
					- Protocol: TCP
					- Port range: 1986
					- Source: 0.0.0.0/0
				Rule 3:
					- IP: IPv4
					- Type: Custom TCP
					- Protocol: TCP
					- Port range: 8003
					- Source: 0.0.0.0/0
				Rule 4:
					- IP: IPv4
					- Type: Custom UDP
					- Protocol: UDP
					- Port range: 5000
					- Source: 0.0.0.0/0
				
			* Outbound:
				- IP: IPv4
				- Type: All traffic
				- Protocol: All
				- Port range: All
				- Destination: 0.0.0.0/0
				
		* Security Group 2:
			Name: baiker-dev-sg-ssh-bastion
			Desciption: Allow SSH access to developers
			VPC: output name of baiker-dev-vpc
			Inbound:
				- IP: IPv4
				- Type: SSH
				- Protocol: TCP
				- Port range: 22
				- Source: *************/32
			Outbound:
				- IP: IPv4
				- Type: All traffic
				- Protocol: All
				- Port range: All
				- Destination: 0.0.0.0/0
		
		* Security Group 3:
			Name: baiker-dev-sg-rds-postgre
			Desciption: Allow connect to Postgres Database
			VPC: output name of baiker-dev-vpc
			Inbound:
				- IP: IPv4
				- Type: PostgreSQL
				- Protocol: TCP
				- Port range: 5454
				- Source: *************/32
			Outbound:
				- IP: IPv4
				- Type: All traffic
				- Protocol: All
				- Port range: All
				- Destination: 0.0.0.0/0
				
		* Security Group 4:
			Name: baiker-dev-redis-db
			Desciption: Allow connect to MemoryDB Redis
			VPC: output name of baiker-dev-vpc
			Inbound:
				- IP: IPv4
				- Type: PostgreSQL
				- Protocol: TCP
				- Port range: 5454
				- Source: *************/32
			Outbound:
				- IP: IPv4
				- Type: All traffic
				- Protocol: All
				- Port range: All
				- Destination: 0.0.0.0/0
		
		* Security Group 5:
			Name: baiker-dev-sg-nlb
			Desciption: Allow Port for Network Load Balancer
			VPC: output name of baiker-dev-vpc
			Inbound:
				Rule 1:
					- IP: IPv4
					- Type: Custom TCP
					- Protocol: TCP
					- Port range: 4999
					- Source: 0.0.0.0/0
				Rule 2:
					- IP: IPv4
					- Type: Custom TCP
					- Protocol: TCP
					- Port range: 9999
					- Source: 0.0.0.0/0
				Rule 3:
					- IP: IPv4
					- Type: Custom TCP
					- Protocol: TCP
					- Port range: 1986
					- Source: 0.0.0.0/0
				Rule 3:
					- IP: IPv4
					- Type: Custom TCP
					- Protocol: TCP
					- Port range: 8003
					- Source: 0.0.0.0/0
				Rule 4:
					- IP: IPv4
					- Type: Custom UDP
					- Protocol: UDP
					- Port range: 5000
					- Source: 0.0.0.0/0
			Outbound:
				- IP: IPv4
				- Type: All traffic
				- Protocol: All
				- Port range: All
				- Destination: 0.0.0.0/0

 - 1 Key Pair:
		Name: baiker-dev-key-pair-ec2
		Type: ED25519
		Key File Format: .pem
		
 - 2 EC2 instanace: (Using Ubuntu 24.04):
	* Bastion Host Instance:
		Name: baiker-dev-ec2-bastion-host
		Amazon Machine Image (AMI):
			+ AMI ID: ami-054400ced365b82a0
			+ Architecture: 64-bit (x86)
		Type: t2.micro
		Key pair: output name of baiker-dev-key-pair-ec2
		Network settings:
			+ VPC: output name of baiker-dev-vpc
			+ Subnet: in a public subnet
			+ Auto-assign public IP: Enable
			+ Firewall (security groups): output of baiker-dev-sg-ssh-bastion
			+ Network Interface:
				Subnet: Same subnet of Bastion Host Instance
				Interface Type: ENA
				Private IPv4 address: Auto-assign
		User Data:
			"apt update && apt upgrade -y  && apt autoremove -y && apt install -y build-essential cmake make zip unzip tar wget curl vim"
			
		
	* Main Instance: (Using Ubuntu 24.04 - using GPU):
		Name: baiker-dev-ec2
		Type: t2.medium
		Amazon Machine Image (AMI):
			+ AMI ID: ami-054400ced365b82a0
			+ Architecture: 64-bit (x86)
		Key pair: output name of baiker-dev-key-pair-ec2
		Network settings:
			+ VPC: output name of baiker-dev-vpc
			+ Subnet: in a public subnet
			+ Auto-assign public IP: Enable
			+ Firewall (security groups): output of baiker-dev-sg-ssh-bastion
			+ Network Interface:
				Subnet: Same subnet of Bastion Host Instance
				Interface Type: ENA
				Private IPv4 address: Auto-assign
		User Data:
			"apt update && apt upgrade -y  && apt autoremove -y && apt install -y build-essential cmake make zip unzip tar wget curl vim
			for pkg in docker.io docker-doc docker-compose docker-compose-v2 podman-docker containerd runc; do sudo apt-get remove $pkg; done
			# Add Docker's official GPG key:
			sudo apt-get update
			sudo apt-get install ca-certificates curl
			sudo install -m 0755 -d /etc/apt/keyrings
			sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
			sudo chmod a+r /etc/apt/keyrings/docker.asc

			# Add the repository to Apt sources:
			echo \
			  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
			  $(. /etc/os-release && echo "${UBUNTU_CODENAME:-$VERSION_CODENAME}") stable" | \
			  sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
			sudo apt-get update
			sudo apt-get install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin -y
			usermod -aG docker $USER"
	Target Groups:
		Name: baiker-dev-tg-humanai
		Target Type: Instance
		Port: 9999
		Protocol: TCP
		IP address type: IPv4
		VPC: follow VPC of ECS
	Network Load Balancer:
		Name: entanglement-nlb-dev-v1
		Scheme: Internal
		Load balancer IP address type: IPv4
		VPC: entanglement-dev-vpc-v1
		Security Group:
		* Name: security-group-for-network-load-balancer-dev-v1
		* Inbound:
			- IP: IPv4
			- Type: Custom TCP
			- Protocol: TCP
			- Port range: 9999
			- Source: 0.0.0.0/0
		* Outbound:
			- IP: IPv4
			- Type: All traffic
			- Protocol: All
			- Port range: All
			- Destination: 0.0.0.0/0
		Protocol: TCP
		Port:7007
		Target group: entanglement-tg-dev-v1

 - 1 Elastic Container Registry:
	Namespace: dev
	repo-name: entanglement
	Image tab mutability: Mutable
	Encrypt: AES-256

 - 1 Cognito:
	Application type: Mobile App
	Name: baiker-dev-cognito
	Cogfigure options:
	
 - 2 S3:
	Terraform state bucket:
		Bucket type: General purpose
		Name: baiker-dev-s3-terraform-state
		Object Ownership: ACLs disable
		Block Public Access settings for this bucket: Block all public access
		Bucket Versioning: disable
	Terraform state bucket:
		Bucket type: General purpose
		Name: baiker-dev-s3-raw-data
		Object Ownership: ACLs disable
		Block Public Access settings for this bucket: Block all public access
		Bucket Versioning: disable
 - 1 Amazon MemoryDB:
	Creation method: 
		+ Single-Region cluster
		+ Create new cluster
	Name: baiker-dev-redis-db
	Connectivity:
		+ Network type: IPv4
		+ Subnet groups:??????????
	Cluster settings:
		+ Engine: Redis
		+ Version: 7.1
		+ Port: 6369
		+ Parameter groups: default.memorydb-redis7
		+ Node type: db.r7g.large
		+ Number of shards: 1
		+ Replica nodes per shard: 1
 - 1 API Gateway:
	- VPC links:
		+ VPC link for REST APIs
		+ Name: entanglement-rest-api-dev-v1
		+ Target NLB: entanglement-nlb-dev-v1
	- 1 API:
		+ Namne: entanglement-api-dev-v1
		+ API endpoint type: Regional
		+ stage: dev-v1
	- 1 yaml file:
		+ @router.get("/chatbot/{convo_id}",						
		+ @router.post("/chat/webrtc")								
		+ @router.get("/chat/stop-session")							
		+ @router.post("/livechat/webrtc/audio-output")			
		+ @router.post("/livechat/webrtc/text-output")			
		+ @router.get("/livechat/stop-session")					
And generate follow this structure:
.
├── README.md
├── deployments
│   ├── dev
│   │   ├── aws-init
│   │   │   ├── ap-northeast-1
│   │   │   │   └── region.hcl
│   │   │   └── area.hcl
│   │   ├── aws-services
│   │   │   ├── ap-northeast-1
│   │   │   │   └── region.hcl
│   │   │   └── area.hcl
│   │   ├── aws-utilities
│   │   │   ├── ap-northeast-1
│   │   │   │   ├── region.hcl
│   │   │   │   └── sync-cognito-database-lambda
│   │   │   │       └── terragrunt.hcl
│   │   │   └── area.hcl
│   │   ├── deployment.hcl
│   │   └── terragrunt.hcl
│   ├── infra
│   │   ├── aws-init
│   │   │   ├── area.hcl
│   │   │   └── us-east-1
│   │   │       └── region.hcl
│   │   ├── aws-services
│   │   │   ├── area.hcl
│   │   │   └── us-east-1
│   │   │       └── region.hcl
│   │   ├── aws-utilities
│   │   │   ├── area.hcl
│   │   │   └── us-east-1
│   │   │       ├── lambda-sync-cognito-database
│   │   │       │   └── terragrunt.hcl
│   │   │       └── region.hcl
│   │   ├── deployment.hcl
│   │   └── terragrunt.hcl
│   └── prod
│       ├── aws-init
│       │   ├── area.hcl
│       │   └── us-east-1
│       │       └── region.hcl
│       ├── aws-services
│       │   ├── area.hcl
│       │   └── us-east-1
│       │       └── region.hcl
│       ├── aws-utilities
│       │   ├── area.hcl
│       │   └── us-east-1
│       │       └── region.hcl
│       ├── deployment.hcl
│       └── terragrunt.hcl
└── terraform
    └── modules
        ├── api
        │   └── api-gateway
        │       ├── README.md
        │       ├── main.tf
        │       ├── outputs.tf
        │       └── variables.tf
        ├── compute
        │   ├── ec2
        │   │   ├── README.md
        │   │   ├── main.tf
        │   │   ├── outputs.tf
        │   │   └── variables.tf
        │   ├── ecr
        │   │   ├── README.md
        │   │   ├── main.tf
        │   │   ├── outputs.tf
        │   │   └── variables.tf
        │   └── ecs
        │       ├── README.md
        │       ├── main.tf
        │       ├── outputs.tf
        │       └── variables.tf
        ├── database
        │   ├── rds
        │   │   ├── README.md
        │   │   ├── main.tf
        │   │   ├── outputs.tf
        │   │   └── variables.tf
        │   └── redis
        │       ├── README.md
        │       ├── main.tf
        │       ├── outputs.tf
        │       └── variables.tf
        ├── lambda
        │   ├── README.md
        │   └── lambda-sync-cognito-database
        │       ├── deployment.zip
        │       ├── input.tf
        │       ├── main.tf
        │       ├── output.tf
        │       └── role.tf
        ├── networking
        │   ├── load-balancer
        │   │   ├── README.md
        │   │   ├── main.tf
        │   │   ├── outputs.tf
        │   │   └── variables.tf
        │   └── vpc
        │       ├── README.md
        │       ├── main.tf
        │       ├── outputs.tf
        │       └── variables.tf
        ├── security
        │   ├── cognito
        │   │   ├── README.md
        │   │   ├── main.tf
        │   │   ├── outputs.tf
        │   │   └── variables.tf
        │   ├── iam
        │   │   ├── README.md
        │   │   ├── main.tf
        │   │   ├── outputs.tf
        │   │   └── variables.tf
        │   └── security-groups
        │       ├── README.md
        │       ├── main.tf
        │       ├── outputs.tf
        │       └── variables.tf
        └── storage
            └── s3
                ├── README.md
                ├── main.tf
                ├── outputs.tf
                └── variables.tf