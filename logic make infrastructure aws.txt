I want to generate code terraform for:
- 1 VPC:
	Name: baiker-dev-vpc
	IPv4: 10.0.0.0/16
	IPv6: No IPv6 CIDR block
	Main network ACL: true
	Main route table: true (3 main routable: baiker-app-sn-rtb, baiker-web-sn-rtb, baiker-db-sn-rtb)
	Number of Availability Zones (AZs): 2
		+ AZ 1: ap-northeast-1a
		+ AZ 2: ap-northeast-1c
	Number of public subnets: 2
	Number of private subnets: 4
		Public:
			+ Public subnet 1:
				* Name: baiker-web-pl-sn-a
				* IPv4 CIDR block: 10.0.0.0/22
			+ Public subnet 2:
				* Name: baiker-web-pl-sn-c
				* IPv4 CIDR block: ********/22
		Private App(ECS, EC2 GPU)
			+ Private subnet 1: 
				* Name: baiker-app-sn-a
				* IPv4 CIDR block: ********/22
			+ Private subnet 2: 
				* Name: baiker-app-sn-c
				* IPv4 CIDR block: *********/22
		
		DB Subnets (RDS):
			+ Private subnet 3: 
				* Name: baiker-rds-sn-a
				* IPv4 CIDR block: *********/22
			+ Private subnet 3: 
				* Name: baiker-rds-sn-c
				* IPv4 CIDR block: *********/22
		Redis Subnets (MemoryDB / ElastiCache):
			+ Private subnet 4: 
				* Name: baiker-redis-sn-a
				* IPv4 CIDR block: *********/20
	Number of NAT gateways: 1
	NAT Gateway:  1 per AZ
	VPC Endpoint: Interface, Gateway endpoint for S3
	DNS options:
		- Enable DNS hostname: yes
		- Enable DNS resolution: yes

- 5 Security Group:
	* Security Group 1:
		Name: baiker-dev-sg
		Desciption: Allow Port to EC2 using GPU
		VPC: output name of baiker-dev-vpc
		Inbound:
			Rule 1:
				- IP: IPv4
				- Type: All SSH
				- Protocol: TCP
				- Port range: 22
				- Source: baiker-dev-sg-ssh-bastion
			Rule 2:
				- IP: IPv4
				- Type: Custom TCP
				- Protocol: TCP
				- Port range: 9999
				- Source:baiker-dev-sg-nlb
			Rule 3:
				- IP: IPv4
				- Type: Custom TCP
				- Protocol: TCP
				- Port range: 1986
				- Source: baiker-dev-sg-nlb
			Rule 4:
				- IP: IPv4
				- Type: Custom TCP
				- Protocol: TCP
				- Port range: 8003
				- Source: baiker-dev-sg-nlb
			Rule 5:
				- IP: IPv4
				- Type: Custom UDP
				- Protocol: UDP
				- Port range: 5000
				- Source: baiker-dev-sg-nlb
			
		* Outbound:
			- IP: IPv4
			- Type: All traffic
			- Protocol: All
			- Port range: All
			- Destination: 0.0.0.0/0
			
	* Security Group 2:
		Name: baiker-dev-sg-ssh-bastion
		Desciption: Allow SSH access to developers
		VPC: output name of baiker-dev-vpc
		Inbound:
			- IP: IPv4
			- Type: SSH
			- Protocol: TCP
			- Port range: 22
			- Source: *************/32
		Outbound:
			- IP: IPv4
			- Type: All traffic
			- Protocol: All
			- Port range: All
			- Destination: 0.0.0.0/0
	
	* Security Group 3:
		Name: baiker-dev-sg-rds-postgre
		Desciption: Allow connect to Postgres Database
		VPC: output name of baiker-dev-vpc
		Inbound:
			- IP: IPv4
			- Type: PostgreSQL
			- Protocol: TCP
			- Port range: 5454
			- Source: *************/32
		Outbound:
			- IP: IPv4
			- Type: All traffic
			- Protocol: All
			- Port range: All
			- Destination: 0.0.0.0/0
			
	* Security Group 4:
		Name: baiker-dev-redis-db
		Desciption: Allow connect to MemoryDB Redis
		VPC: output name of baiker-dev-vpc
		Inbound:
			- IP: IPv4
			- Type: Custom TCP
			- Protocol: TCP
			- Port range: 6369
			- Source: *************/32
		Outbound:
			- IP: IPv4
			- Type: All traffic
			- Protocol: All
			- Port range: All
			- Destination: 0.0.0.0/0
	
	* Security Group 5:
		Name: baiker-dev-sg-nlb
		Desciption: Allow Port for Network Load Balancer
		VPC: output name of baiker-dev-vpc
		Inbound:
			Rule 1:
				- IP: IPv4
				- Type: Custom TCP
				- Protocol: TCP
				- Port range: 4999
				- Source: 0.0.0.0/0
			Rule 2:
				- IP: IPv4
				- Type: Custom TCP
				- Protocol: TCP
				- Port range: 9999
				- Source: 0.0.0.0/0
			Rule 3:
				- IP: IPv4
				- Type: Custom TCP
				- Protocol: TCP
				- Port range: 1986
				- Source: 0.0.0.0/0
			Rule 4:
				- IP: IPv4
				- Type: Custom TCP
				- Protocol: TCP
				- Port range: 8003
				- Source: 0.0.0.0/0
			Rule 5:
				- IP: IPv4
				- Type: Custom UDP
				- Protocol: UDP
				- Port range: 5000
				- Source: 0.0.0.0/0
		Outbound:
			- IP: IPv4
			- Type: All traffic
			- Protocol: All
			- Port range: All
			- Destination: 0.0.0.0/0

- 1 Key Pair - AWS Key Pair not Key Pari on Local:
	Name: baiker-dev-key-pair-ec2
	Type: ED25519
	Key File Format: .pem
		
- 2 EC2 instanace: (Using Ubuntu 24.04):
	* Bastion Host Instance:
		Name: baiker-dev-ec2-bastion-host
		Amazon Machine Image (AMI):
			+ AMI ID: ami-054400ced365b82a0
			+ Architecture: 64-bit (x86)
		Type: t2.micro
		Key pair: output name of baiker-dev-key-pair-ec2
		Network settings:
			+ VPC: output name of baiker-dev-vpc
			+ Subnet: in a public subnet
			+ Auto-assign public IP: Enable
			+ Firewall (security groups): output of baiker-dev-sg-ssh-bastion
			+ Network Interface:
				Subnet: Same subnet of Bastion Host Instance
				Interface Type: ENA
				Private IPv4 address: Auto-assign
		User Data:
			"apt update && apt upgrade -y  && apt autoremove -y && apt install -y build-essential cmake make zip unzip tar wget curl vim"
			
	
	* Main Instance: (Using Ubuntu 24.04 - using GPU):
		Name: baiker-dev-ec2
		Type: t2.medium
		Amazon Machine Image (AMI):
			+ AMI ID: ami-054400ced365b82a0
			+ Architecture: 64-bit (x86)
		Key pair: output name of baiker-dev-key-pair-ec2
		Network settings:
			+ VPC: output name of baiker-dev-vpc
			+ Subnet: in a public subnet
			+ Auto-assign public IP: Enable
			+ Security group: output of baiker-dev-sg
			+ Network Interface:
				Subnet: Same subnet of Bastion Host Instance
				Interface Type: ENA
				Private IPv4 address: Auto-assign
		User Data:
			"apt update && apt upgrade -y  && apt autoremove -y && apt install -y build-essential cmake make zip unzip tar wget curl vim
			for pkg in docker.io docker-doc docker-compose docker-compose-v2 podman-docker containerd runc; do sudo apt-get remove $pkg; done
			# Add Docker's official GPG key:
			sudo apt-get update
			sudo apt-get install ca-certificates curl
			sudo install -m 0755 -d /etc/apt/keyrings
			sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
			sudo chmod a+r /etc/apt/keyrings/docker.asc

			# Add the repository to Apt sources:
			echo \
				"deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
				$(. /etc/os-release && echo "${UBUNTU_CODENAME:-$VERSION_CODENAME}") stable" | \
				sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
			sudo apt-get update
			sudo apt-get install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin -y
			usermod -aG docker $USER"


- 1 Elastic Load Balancing:
	*5 Target Group:
		HumanAI:
			+ Name: baiker-dev-tg-humanai
			+ Target Type: Instance
			+ Port: 9999
			+ Protocol: TCP
			+ IP address type: IPv4
			+ VPC: baiker-dev-vpc
		SRS:
			+ Name: baiker-dev-tg-srs
			+ Target Type: Instance
			+ Port: 1986
			+ Protocol: TCP
			+ IP address type: IPv4
			+ VPC: baiker-dev-vpc
		SRS-Stream:
			+ Name: baiker-dev-tg-srs-stream
			+ Target Type: Instance
			+ Port: 5000
			+ Protocol: UDP
			+ IP address type: IPv4
			+ VPC: baiker-dev-vpc

	* Network Load Balancer:
		Name: baiker-dev-nlb
		Scheme: Internal
		Load balancer IP address type: IPv4
		VPC: baiker-dev-vpc
		Subnets: baiker-dev-private-subnet-1, baiker-dev-private-subnet-2
		Security Groups: baiker-dev-sg-nlb
		IP address type: IPv4
		Listener:
			Humanai:
				+ Protocol: TCP
				+ Port: 9999
				+ Target group: baiker-dev-tg-humanai
				+ Default action: Forward to baiker-dev-tg-humanai
			SRS:
				+ Protocol: TCP
				+ Port: 1986
				+ Target group: baiker-dev-tg-srs
				+ Default action: Forward to baiker-dev-tg-srs
			SRS-Stream:
				+ Protocol: UDP
				+ Port: 5000
				+ Target group: baiker-dev-tg-srs-stream
				+ Default action: Forward to baiker-dev-tg-srs-stream



- 1 Aurora and RDS: Postgre DB
	Name: baiker-dev-rds-postgre
	Engine: PostgreSQL
	Version: PostgreSQL 17.4-R1
	Template: Dev/Test
	Availability and durability: Single-AZ
	Settings:
		+ DB instance identifier: baiker-dev-rds-postgre
		+ Master username: baiker
		+ Credentials Settings: Auto generate password
	Instance type: db.m7g.large
	Storage Type: General Purpose SSD (gp3)
	Storage: 50 GiB
	Storage auto scaling: Enable
	Connectivity:
		+ Computer source: baiker-dev-ec2
		+ VPC: baiker-dev-vpc
		+ Subnet group: baiker-dev-rds-subnet-group
		+ Security group: baiker-dev-sg-rds-postgre
	Database Port: 5454
	Database Authentication: Password authentication
	Monitoring: Database Insights - Standard
	Performance Insights: Enable
	Enhanced Monitoring: Enable
	Initial Database Name: baiker_dev
	Backup: Enable
	Maintainance: Enable


- 1 Elastic Container Registry:
	Namespace: dev
	repo-name: entanglement
	Image tab mutability: Mutable
	Encrypt: AES-256

- 1 Cognito:
	Application type: Mobile App
	Name: baiker-dev-cognito
	Configure options:
		User Pool Settings:
			+ Pool name: baiker-dev-user-pool
			+ Sign-in options: Email, Username
			+ Password policy:
				- Minimum length: 6 characters
				- Require uppercase letters: No
				- Require lowercase letters: No
				- Require numbers: No
				- Require special characters: No
			+ MFA configuration: Optional
			+ Account recovery: Email only
			+ User account confirmation: Email verification required
			+ Email configuration: Cognito
		
		User Pool Client Settings:
			+ App client name: baiker-dev-mobile-client
			+ App client type: Public client
			+ Authentication flows:
				- ALLOW_USER_SRP_AUTH: Yes
				- ALLOW_REFRESH_TOKEN_AUTH: Yes
				- ALLOW_USER_PASSWORD_AUTH: Yes
			+ OAuth 2.0 settings:
				- OAuth flows: Authorization code grant
				- OAuth scopes: openid, email, profile
				- Callback URLs: com.baiker.mobile://callback
				- Sign out URLs: com.baiker.mobile://signout
			+ Advanced app client settings:
				- Access token expiration: 1 hour
				- ID token expiration: 1 hour
				- Refresh token expiration: 30 days
				- Refresh token rotation: Enabled
		
		Identity Pool (Federated Identities):
			+ Identity pool name: baiker-dev-identity-pool
			+ Enable access to unauthenticated identities: No
			+ Authentication providers: Cognito User Pool
			+ IAM roles:
				- Authenticated role: baiker-dev-cognito-authenticated-role
				- Unauthenticated role: baiker-dev-cognito-unauthenticated-role
	
	
- 2 S3:
	Terraform state bucket:
		Bucket type: General purpose
		Name: baiker-dev-s3-terraform-state
		Object Ownership: ACLs disable
		Block Public Access settings for this bucket: Block all public access
		Bucket Versioning: disable
	Terraform state bucket:
		Bucket type: General purpose
		Name: baiker-dev-s3-raw-data
		Object Ownership: ACLs disable
		Block Public Access settings for this bucket: Block all public access
		Bucket Versioning: disable

- 1 Amazon MemoryDB:
	Creation method: 
		+ Single-Region cluster
		+ Create new cluster
	Name: baiker-dev-redis-db
	Connectivity:
		+ Network type: IPv4
		+ Subnet groups:baiker-dev-private-subnet-3, baiker-dev-private-subnet-4
	Cluster settings:
		+ Engine: Redis
		+ Version: 7.1
		+ Port: 6369
		+ Parameter groups: default.memorydb-redis7
		+ Node type: db.r7g.large
		+ Number of shards: 1
		+ Replica nodes per shard: 1

				
And generate follow this structure:
.
├── README.md
├── deployments
│   ├── dev
│   │   ├── aws-init
│   │   │   ├── ap-northeast-1
│   │   │   │   └── region.hcl
│   │   │   └── area.hcl
│   │   ├── aws-services
│   │   │   ├── ap-northeast-1
│   │   │   │   └── region.hcl
│   │   │   └── area.hcl
│   │   ├── aws-utilities
│   │   │   ├── ap-northeast-1
│   │   │   │   ├── region.hcl
│   │   │   │   └── sync-cognito-database-lambda
│   │   │   │       └── terragrunt.hcl
│   │   │   └── area.hcl
│   │   ├── deployment.hcl
│   │   └── terragrunt.hcl
│   ├── infra
│   │   ├── aws-init
│   │   │   ├── area.hcl
│   │   │   └── us-east-1
│   │   │       └── region.hcl
│   │   ├── aws-services
│   │   │   ├── area.hcl
│   │   │   └── us-east-1
│   │   │       └── region.hcl
│   │   ├── aws-utilities
│   │   │   ├── area.hcl
│   │   │   └── us-east-1
│   │   │       ├── lambda-sync-cognito-database
│   │   │       │   └── terragrunt.hcl
│   │   │       └── region.hcl
│   │   ├── deployment.hcl
│   │   └── terragrunt.hcl
│   └── prod
│       ├── aws-init
│       │   ├── area.hcl
│       │   └── us-east-1
│       │       └── region.hcl
│       ├── aws-services
│       │   ├── area.hcl
│       │   └── us-east-1
│       │       └── region.hcl
│       ├── aws-utilities
│       │   ├── area.hcl
│       │   └── us-east-1
│       │       └── region.hcl
│       ├── deployment.hcl
│       └── terragrunt.hcl
└── terraform
    └── modules
        ├── api
        │   └── api-gateway
        │       ├── README.md
        │       ├── main.tf
        │       ├── outputs.tf
        │       └── variables.tf
        ├── compute
        │   ├── ec2
        │   │   ├── README.md
        │   │   ├── main.tf
        │   │   ├── outputs.tf
        │   │   └── variables.tf
        │   ├── ecr
        │   │   ├── README.md
        │   │   ├── main.tf
        │   │   ├── outputs.tf
        │   │   └── variables.tf
        │   └── ecs
        │       ├── README.md
        │       ├── main.tf
        │       ├── outputs.tf
        │       └── variables.tf
        ├── database
        │   ├── rds
        │   │   ├── README.md
        │   │   ├── main.tf
        │   │   ├── outputs.tf
        │   │   └── variables.tf
        │   └── redis
        │       ├── README.md
        │       ├── main.tf
        │       ├── outputs.tf
        │       └── variables.tf
        ├── lambda
        │   ├── README.md
        │   └── lambda-sync-cognito-database
        │       ├── deployment.zip
        │       ├── input.tf
        │       ├── main.tf
        │       ├── output.tf
        │       └── role.tf
        ├── networking
        │   ├── load-balancer
        │   │   ├── README.md
        │   │   ├── main.tf
        │   │   ├── outputs.tf
        │   │   └── variables.tf
        │   └── vpc
        │       ├── README.md
        │       ├── main.tf
        │       ├── outputs.tf
        │       └── variables.tf
        ├── security
        │   ├── cognito
        │   │   ├── README.md
        │   │   ├── main.tf
        │   │   ├── outputs.tf
        │   │   └── variables.tf
        │   ├── iam
        │   │   ├── README.md
        │   │   ├── main.tf
        │   │   ├── outputs.tf
        │   │   └── variables.tf
        │   └── security-groups
        │       ├── README.md
        │       ├── main.tf
        │       ├── outputs.tf
        │       └── variables.tf
        └── storage
            └── s3
                ├── README.md
                ├── main.tf
                ├── outputs.tf
                └── variables.tf