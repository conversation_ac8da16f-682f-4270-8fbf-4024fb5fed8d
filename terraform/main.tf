# Terraform configuration
terraform {
  required_version = ">= 1.5"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
    tls = {
      source  = "hashicorp/tls"
      version = "~> 4.0"
    }
  }
}

# AWS Provider
provider "aws" {
  region = var.aws_region
  
  default_tags {
    tags = var.default_tags
  }
}

# Local values
locals {
  name_prefix = "${var.project_name}-${var.environment}"
  
  common_tags = merge(var.default_tags, {
    Project     = var.project_name
    Environment = var.environment
    ManagedBy   = "terraform"
  })
}

# VPC Module
module "vpc" {
  source = "./modules/networking/vpc"

  vpc_name               = "${local.name_prefix}-vpc"
  vpc_cidr              = var.vpc_cidr
  availability_zones    = var.availability_zones
  public_subnet_cidrs   = var.public_subnet_cidrs
  private_subnet_cidrs  = var.private_subnet_cidrs
  enable_nat_gateway    = true
  single_nat_gateway    = true
  enable_dns_hostnames  = true
  enable_dns_support    = true

  tags = local.common_tags
}

# Security Groups Module
module "security_groups" {
  source = "./modules/security/security-groups"

  vpc_id           = module.vpc.vpc_id
  allowed_ssh_cidr = var.allowed_ssh_cidr

  tags = local.common_tags
}

# EC2 Module
module "ec2" {
  source = "./modules/compute/ec2"

  vpc_id                    = module.vpc.vpc_id
  public_subnet_ids         = module.vpc.public_subnet_ids
  bastion_security_group_id = module.security_groups.ssh_bastion_security_group_id
  main_security_group_id    = module.security_groups.main_ec2_security_group_id
  
  key_pair_name         = "${local.name_prefix}-key-pair-ec2"
  ami_id               = var.ami_id
  bastion_instance_type = var.bastion_instance_type
  main_instance_type    = var.main_instance_type

  tags = local.common_tags
}

# Load Balancer Module
module "load_balancer" {
  source = "./modules/networking/load-balancer"

  vpc_id                = module.vpc.vpc_id
  private_subnet_ids    = slice(module.vpc.private_subnet_ids, 0, 2)  # Use first 2 private subnets
  nlb_security_group_id = module.security_groups.nlb_security_group_id
  target_instance_ids   = [module.ec2.main_instance_id]

  tags = local.common_tags
}

# RDS Module
module "rds" {
  source = "./modules/database/rds"

  vpc_id                = module.vpc.vpc_id
  private_subnet_ids    = module.vpc.private_subnet_ids
  rds_security_group_id = module.security_groups.rds_postgres_security_group_id

  db_name                = var.db_name
  db_instance_identifier = "${local.name_prefix}-rds-postgre"
  db_username            = var.db_username
  db_port               = var.db_port
  
  engine_version = var.postgres_engine_version
  instance_class = var.rds_instance_class
  
  allocated_storage     = var.rds_allocated_storage
  max_allocated_storage = var.rds_max_allocated_storage
  storage_type         = "gp3"
  
  backup_retention_period = 7
  multi_az               = false
  
  performance_insights_enabled = true
  monitoring_interval         = 60

  tags = local.common_tags
}

# ECR Module
module "ecr" {
  source = "./modules/compute/ecr"

  repository_name      = var.ecr_repository_name
  image_tag_mutability = "MUTABLE"
  scan_on_push        = true
  encryption_type     = "AES256"

  tags = local.common_tags
}

# Cognito Module
module "cognito" {
  source = "./modules/security/cognito"

  user_pool_name     = "${local.name_prefix}-user-pool"
  client_name        = "${local.name_prefix}-mobile-client"
  identity_pool_name = "${local.name_prefix}-identity-pool"

  tags = local.common_tags
}

# S3 Module
module "s3" {
  source = "./modules/storage/s3"

  tags = local.common_tags
}

# Redis Module
module "redis" {
  source = "./modules/database/redis"

  cluster_name            = "${local.name_prefix}-redis-db"
  private_subnet_ids      = slice(module.vpc.private_subnet_ids, 2, 4)  # Use last 2 private subnets
  redis_security_group_id = module.security_groups.redis_security_group_id
  
  node_type              = var.redis_node_type
  num_shards             = 1
  num_replicas_per_shard = 1
  engine_version         = "7.1"
  port                   = 6369

  tags = local.common_tags
}