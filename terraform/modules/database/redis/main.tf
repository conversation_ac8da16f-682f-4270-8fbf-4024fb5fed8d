# MemoryDB Subnet Group
resource "aws_memorydb_subnet_group" "main" {
  name       = "baiker-dev-redis-subnet-group"
  subnet_ids = var.private_subnet_ids

  tags = merge(var.tags, {
    Name = "baiker-dev-redis-subnet-group"
  })
}

# MemoryDB Cluster
resource "aws_memorydb_cluster" "main" {
  name                 = var.cluster_name
  node_type           = var.node_type
  num_shards          = var.num_shards
  num_replicas_per_shard = var.num_replicas_per_shard
  
  engine_version      = var.engine_version
  port               = var.port
  parameter_group_name = var.parameter_group_name
  
  subnet_group_name   = aws_memorydb_subnet_group.main.name
  security_group_ids  = [var.redis_security_group_id]
  
  snapshot_retention_limit = var.snapshot_retention_limit
  snapshot_window         = var.snapshot_window
  maintenance_window      = var.maintenance_window
  
  tls_enabled = true
  
  tags = merge(var.tags, {
    Name = var.cluster_name
  })
}