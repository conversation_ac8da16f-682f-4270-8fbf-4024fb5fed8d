output "cluster_arn" {
  description = "ARN of the MemoryDB cluster"
  value       = aws_memorydb_cluster.main.arn
}

output "cluster_endpoint" {
  description = "DNS hostname of the cluster configuration endpoint"
  value       = aws_memorydb_cluster.main.cluster_endpoint[0].address
}

output "cluster_port" {
  description = "Port number of the cluster"
  value       = aws_memorydb_cluster.main.cluster_endpoint[0].port
}

output "cluster_name" {
  description = "Name of the cluster"
  value       = aws_memorydb_cluster.main.name
}

output "subnet_group_name" {
  description = "Name of the subnet group"
  value       = aws_memorydb_subnet_group.main.name
}