variable "cluster_name" {
  description = "Name of the MemoryDB cluster"
  type        = string
  default     = "baiker-dev-redis-db"
}

variable "private_subnet_ids" {
  description = "List of private subnet IDs"
  type        = list(string)
}

variable "redis_security_group_id" {
  description = "Security group ID for Redis cluster"
  type        = string
}

variable "node_type" {
  description = "Node type for MemoryDB cluster"
  type        = string
  default     = "db.r7g.large"
}

variable "num_shards" {
  description = "Number of shards in the cluster"
  type        = number
  default     = 1
}

variable "num_replicas_per_shard" {
  description = "Number of replica nodes per shard"
  type        = number
  default     = 1
}

variable "engine_version" {
  description = "Redis engine version"
  type        = string
  default     = "7.1"
}

variable "port" {
  description = "Port for Redis cluster"
  type        = number
  default     = 6369
}

variable "parameter_group_name" {
  description = "Name of the parameter group"
  type        = string
  default     = "default.memorydb-redis7"
}

variable "snapshot_retention_limit" {
  description = "Number of days to retain snapshots"
  type        = number
  default     = 7
}

variable "snapshot_window" {
  description = "Daily time range for snapshots"
  type        = string
  default     = "03:00-05:00"
}

variable "maintenance_window" {
  description = "Weekly time range for maintenance"
  type        = string
  default     = "sun:05:00-sun:06:00"
}

variable "tags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
  default     = {}
}