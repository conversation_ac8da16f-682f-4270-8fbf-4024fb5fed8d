# RDS PostgreSQL Module

This module creates an AWS RDS PostgreSQL instance with enhanced security, monitoring, and backup capabilities.

## Features

- PostgreSQL 17.4 database instance
- Encrypted storage with KMS key
- Auto-generated secure password stored in Secrets Manager
- Enhanced monitoring with CloudWatch
- Performance Insights enabled
- Custom parameter group for logging
- Automated backups with 7-day retention
- Multi-AZ deployment support
- Private subnet deployment

## Usage

```hcl
module "rds" {
  source = "./modules/database/rds"

  vpc_id                = module.vpc.vpc_id
  private_subnet_ids    = module.vpc.private_subnet_ids
  rds_security_group_id = module.security_groups.rds_postgres_security_group_id

  db_name                = "baiker_dev"
  db_instance_identifier = "baiker-dev-rds-postgre"
  db_username            = "baiker"
  db_port               = 5454
  
  engine_version = "17.4"
  instance_class = "db.m7g.large"
  
  allocated_storage     = 50
  max_allocated_storage = 200
  storage_type         = "gp3"
  
  backup_retention_period = 7
  multi_az               = false
  
  performance_insights_enabled = true
  monitoring_interval         = 60

  tags = {
    Environment = "dev"
    Project     = "baiker"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| vpc_id | ID of the VPC | `string` | n/a | yes |
| private_subnet_ids | List of private subnet IDs for RDS subnet group | `list(string)` | n/a | yes |
| rds_security_group_id | Security group ID for RDS instance | `string` | n/a | yes |
| db_name | Name of the database | `string` | `"baiker_dev"` | no |
| db_instance_identifier | Identifier for the RDS instance | `string` | `"baiker-dev-rds-postgre"` | no |
| db_username | Username for the master DB user | `string` | `"baiker"` | no |
| db_port | Port for the RDS instance | `number` | `5454` | no |
| engine_version | PostgreSQL engine version | `string` | `"17.4"` | no |
| instance_class | RDS instance class | `string` | `"db.m7g.large"` | no |
| allocated_storage | Initial storage allocation | `number` | `50` | no |
| max_allocated_storage | Maximum storage allocation for auto-scaling | `number` | `200` | no |
| storage_type | Storage type | `string` | `"gp3"` | no |
| backup_retention_period | Backup retention period in days | `number` | `7` | no |
| backup_window | Backup window | `string` | `"03:00-04:00"` | no |
| maintenance_window | Maintenance window | `string` | `"sun:04:00-sun:05:00"` | no |
| multi_az | Enable Multi-AZ deployment | `bool` | `false` | no |
| performance_insights_enabled | Enable Performance Insights | `bool` | `true` | no |
| monitoring_interval | Enhanced monitoring interval | `number` | `60` | no |
| tags | A map of tags to assign to the resource | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| db_instance_id | RDS instance ID |
| db_instance_arn | RDS instance ARN |
| db_instance_endpoint | RDS instance endpoint |
| db_instance_hosted_zone_id | RDS instance hosted zone ID |
| db_instance_identifier | RDS instance identifier |
| db_instance_resource_id | RDS instance resource ID |
| db_instance_status | RDS instance status |
| db_instance_name | RDS instance database name |
| db_instance_username | RDS instance root username (sensitive) |
| db_instance_port | RDS instance port |
| db_subnet_group_id | DB subnet group ID |
| db_subnet_group_arn | DB subnet group ARN |
| db_parameter_group_id | DB parameter group ID |
| db_parameter_group_arn | DB parameter group ARN |
| kms_key_id | KMS key ID for RDS encryption |
| kms_key_arn | KMS key ARN for RDS encryption |
| monitoring_role_arn | Enhanced monitoring IAM role ARN |
| secret_arn | Secrets Manager secret ARN containing database credentials |
| secret_name | Secrets Manager secret name containing database credentials |
| connection_string | PostgreSQL connection string (sensitive) |

## Database Configuration

### Instance Specifications
- **Engine**: PostgreSQL 17.4
- **Instance Class**: db.m7g.large
- **Storage**: 50 GB GP3 (auto-scaling to 200 GB)
- **Port**: 5454 (custom port for security)
- **Multi-AZ**: Disabled (single-AZ for development)

### Security Features
- **Encryption**: KMS encryption for storage and backups
- **Network**: Private subnets only (no public access)
- **Security Groups**: Restricted access via security groups
- **Secrets Management**: Auto-generated password stored in AWS Secrets Manager

### Monitoring & Logging
- **Performance Insights**: Enabled with KMS encryption
- **Enhanced Monitoring**: 60-second interval
- **Parameter Group**: Custom logging configuration
- **CloudWatch Logs**: All statements and slow queries logged

### Backup & Maintenance
- **Automated Backups**: 7-day retention period
- **Backup Window**: 03:00-04:00 UTC
- **Maintenance Window**: Sunday 04:00-05:00 UTC
- **Snapshot**: Copy tags to snapshots enabled

## Database Connection

Retrieve database credentials from AWS Secrets Manager:

```bash
# Get the secret ARN
SECRET_ARN=$(terraform output -raw secret_arn)

# Retrieve database credentials
aws secretsmanager get-secret-value --secret-id $SECRET_ARN --query SecretString --output text | jq

# Connect to database
psql "******************************************/baiker_dev"
```

Or use the connection string output:

```bash
# Get connection string (sensitive output)
terraform output -raw connection_string
```

## Testing

To test this module:

```bash
# Initialize terraform
terraform init

# Plan the deployment
terraform plan

# Apply the configuration
terraform apply

# Verify RDS instance creation
aws rds describe-db-instances --db-instance-identifier "baiker-dev-rds-postgre"

# Check instance status
aws rds describe-db-instances --db-instance-identifier "baiker-dev-rds-postgre" --query 'DBInstances[0].DBInstanceStatus'

# Test database connectivity (from within VPC)
psql "$(terraform output -raw connection_string)" -c "SELECT version();"

# Check Performance Insights
aws pi describe-dimension-keys --service-type RDS --identifier $(terraform output -raw db_instance_resource_id) --metric-type DBLoad --start-time $(date -d '1 hour ago' -u +%Y-%m-%dT%H:%M:%SZ) --end-time $(date -u +%Y-%m-%dT%H:%M:%SZ) --group-by Dimension --group-identifier 'db.sql_tokenized.statement'

# Clean up
terraform destroy
```

## Production Considerations

For production deployment, consider these changes:

```hcl
# Production settings
multi_az                = true
backup_retention_period = 30
deletion_protection     = true
skip_final_snapshot     = false
instance_class          = "db.r7g.xlarge"  # Higher performance
max_allocated_storage   = 1000            # Higher storage limit
```

## Security Best Practices

1. **Network Security**: Database is deployed in private subnets only
2. **Encryption**: All data encrypted at rest and in transit
3. **Access Control**: Security groups restrict network access
4. **Credentials**: Auto-generated strong passwords stored securely
5. **Monitoring**: Enhanced monitoring and logging enabled
6. **Backups**: Automated backups with encryption

## Cost Optimization

- Uses GP3 storage for better price/performance
- Single-AZ deployment for development (enables Multi-AZ for production)
- Appropriate instance sizing for workload
- Performance Insights included in monitoring costs
- 7-day backup retention balances cost and data protection