# Random password for RDS instance
resource "random_password" "master" {
  length  = 16
  special = true
}

# K<PERSON> key for RDS encryption
resource "aws_kms_key" "rds" {
  description             = "KMS key for RDS encryption"
  deletion_window_in_days = 7

  tags = merge(var.tags, {
    Name = "baiker-dev-rds-kms-key"
  })
}

resource "aws_kms_alias" "rds" {
  name          = "alias/baiker-dev-rds"
  target_key_id = aws_kms_key.rds.key_id
}

# RDS Subnet Group
resource "aws_db_subnet_group" "main" {
  name       = "baiker-dev-rds-subnet-group"
  subnet_ids = var.private_subnet_ids

  tags = merge(var.tags, {
    Name = "baiker-dev-rds-subnet-group"
  })
}

# RDS Parameter Group
resource "aws_db_parameter_group" "main" {
  family = "postgres17"
  name   = "baiker-dev-postgres-params"

  parameter {
    name  = "log_statement"
    value = "all"
  }

  parameter {
    name  = "log_min_duration_statement"
    value = "1000"
  }

  parameter {
    name  = "shared_preload_libraries"
    value = "pg_stat_statements"
  }

  tags = merge(var.tags, {
    Name = "baiker-dev-postgres-params"
  })
}

# IAM role for enhanced monitoring
resource "aws_iam_role" "rds_monitoring" {
  name = "baiker-dev-rds-monitoring-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "monitoring.rds.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(var.tags, {
    Name = "baiker-dev-rds-monitoring-role"
  })
}

resource "aws_iam_role_policy_attachment" "rds_monitoring" {
  role       = aws_iam_role.rds_monitoring.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonRDSEnhancedMonitoringRole"
}

# RDS Instance
resource "aws_db_instance" "main" {
  identifier = var.db_instance_identifier

  # Engine settings
  engine         = "postgres"
  engine_version = var.engine_version
  instance_class = var.instance_class

  # Database settings
  db_name  = var.db_name
  username = var.db_username
  password = random_password.master.result
  port     = var.db_port

  # Storage settings
  allocated_storage     = var.allocated_storage
  max_allocated_storage = var.max_allocated_storage
  storage_type          = var.storage_type
  storage_encrypted     = true
  kms_key_id           = aws_kms_key.rds.arn

  # Network settings
  db_subnet_group_name   = aws_db_subnet_group.main.name
  vpc_security_group_ids = [var.rds_security_group_id]
  publicly_accessible    = false
  multi_az              = var.multi_az

  # Parameter and option groups
  parameter_group_name = aws_db_parameter_group.main.name

  # Backup settings
  backup_retention_period = var.backup_retention_period
  backup_window          = var.backup_window
  maintenance_window     = var.maintenance_window
  copy_tags_to_snapshot  = true

  # Monitoring settings
  performance_insights_enabled    = var.performance_insights_enabled
  performance_insights_kms_key_id = aws_kms_key.rds.arn
  monitoring_interval             = var.monitoring_interval
  monitoring_role_arn            = aws_iam_role.rds_monitoring.arn

  # Additional settings
  auto_minor_version_upgrade = true
  deletion_protection       = false  # Set to true for production
  skip_final_snapshot      = true   # Set to false for production

  tags = merge(var.tags, {
    Name = var.db_instance_identifier
  })

  depends_on = [
    aws_db_subnet_group.main,
    aws_db_parameter_group.main,
    aws_iam_role_policy_attachment.rds_monitoring
  ]
}

# Store the password in AWS Secrets Manager
resource "aws_secretsmanager_secret" "db_password" {
  name                    = "baiker-dev/rds/master-password"
  description            = "Master password for Baiker Dev RDS instance"
  recovery_window_in_days = 7

  tags = merge(var.tags, {
    Name = "baiker-dev-rds-master-password"
  })
}

resource "aws_secretsmanager_secret_version" "db_password" {
  secret_id = aws_secretsmanager_secret.db_password.id
  secret_string = jsonencode({
    username = var.db_username
    password = random_password.master.result
    engine   = "postgres"
    host     = aws_db_instance.main.endpoint
    port     = var.db_port
    dbname   = var.db_name
  })
}