variable "vpc_id" {
  description = "ID of the VPC"
  type        = string
}

variable "public_subnet_ids" {
  description = "List of public subnet IDs"
  type        = list(string)
}

variable "bastion_security_group_id" {
  description = "Security group ID for bastion host"
  type        = string
}

variable "main_security_group_id" {
  description = "Security group ID for main EC2 instance"
  type        = string
}

variable "key_pair_name" {
  description = "Name of the key pair"
  type        = string
  default     = "baiker-dev-key-pair-ec2"
}

variable "ami_id" {
  description = "AMI ID for EC2 instances"
  type        = string
  default     = "ami-054400ced365b82a0"  # Ubuntu 24.04 LTS in ap-northeast-1
}

variable "bastion_instance_type" {
  description = "Instance type for bastion host"
  type        = string
  default     = "t2.micro"
}

variable "main_instance_type" {
  description = "Instance type for main EC2 instance"
  type        = string
  default     = "t2.medium"
}

variable "tags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
  default     = {}
}