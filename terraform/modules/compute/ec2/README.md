# EC2 Module

This module creates EC2 instances, key pairs, and associated resources for the Baiker development environment.

## Features

- Generates AWS-managed ED25519 key pair automatically
- Stores private key securely in AWS Secrets Manager
- Provisions bastion host (t2.micro) in public subnet
- Provisions main EC2 instance (t2.medium) in public subnet
- Configures user data scripts for system setup
- Assigns Elastic IPs for static access
- Enables EBS encryption for security

## Usage

```hcl
module "ec2" {
  source = "./modules/compute/ec2"

  vpc_id                    = module.vpc.vpc_id
  public_subnet_ids         = module.vpc.public_subnet_ids
  bastion_security_group_id = module.security_groups.ssh_bastion_security_group_id
  main_security_group_id    = module.security_groups.main_ec2_security_group_id
  
  key_pair_name         = "baiker-dev-key-pair-ec2"
  ami_id               = "ami-054400ced365b82a0"  # Ubuntu 24.04 LTS
  bastion_instance_type = "t2.micro"
  main_instance_type    = "t2.medium"

  tags = {
    Environment = "dev"
    Project     = "baiker"
  }
}
```

## Prerequisites

**No SSH key generation required!** This module automatically generates an AWS-managed ED25519 key pair and stores the private key in AWS Secrets Manager for secure access.

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| vpc_id | ID of the VPC | `string` | n/a | yes |
| public_subnet_ids | List of public subnet IDs | `list(string)` | n/a | yes |
| bastion_security_group_id | Security group ID for bastion host | `string` | n/a | yes |
| main_security_group_id | Security group ID for main EC2 instance | `string` | n/a | yes |
| key_pair_name | Name of the key pair | `string` | `"baiker-dev-key-pair-ec2"` | no |
| ami_id | AMI ID for EC2 instances | `string` | `"ami-054400ced365b82a0"` | no |
| bastion_instance_type | Instance type for bastion host | `string` | `"t2.micro"` | no |
| main_instance_type | Instance type for main EC2 instance | `string` | `"t2.medium"` | no |
| tags | A map of tags to assign to the resource | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| key_pair_name | Name of the key pair |
| key_pair_id | ID of the key pair |
| private_key_secret_arn | ARN of the secret containing the private key |
| private_key_secret_name | Name of the secret containing the private key |
| public_key_fingerprint | SHA256 fingerprint of the public key |
| bastion_instance_id | ID of the bastion instance |
| bastion_instance_private_ip | Private IP of the bastion instance |
| bastion_instance_public_ip | Public IP of the bastion instance |
| bastion_eip | Elastic IP of the bastion instance |
| main_instance_id | ID of the main instance |
| main_instance_private_ip | Private IP of the main instance |
| main_instance_public_ip | Public IP of the main instance |
| main_eip | Elastic IP of the main instance |
| instance_ids | Map of instance IDs |

## Instance Details

### Bastion Host (baiker-dev-ec2-bastion-host)
- **Type**: t2.micro
- **OS**: Ubuntu 24.04 LTS
- **Storage**: 20 GB encrypted GP3
- **Network**: Public subnet with Elastic IP
- **Purpose**: Secure SSH access point
- **User Data**: Installs basic development tools

### Main Instance (baiker-dev-ec2)
- **Type**: t2.medium
- **OS**: Ubuntu 24.04 LTS
- **Storage**: 50 GB encrypted GP3
- **Network**: Public subnet with Elastic IP
- **Purpose**: Main application server
- **User Data**: Installs development tools + Docker

## User Data Scripts

### Bastion Host
- Updates system packages
- Installs: build-essential, cmake, make, zip, unzip, tar, wget, curl, vim

### Main Instance
- Updates system packages
- Installs development tools
- Removes old Docker packages
- Installs latest Docker CE with plugins
- Adds ubuntu user to docker group

## SSH Access

After deployment, retrieve the private key and access instances:

### Step 1: Retrieve Private Key from AWS Secrets Manager
```bash
# Get the private key secret ARN
SECRET_ARN=$(terraform output -raw private_key_secret_arn)

# Download and save the private key
aws secretsmanager get-secret-value --secret-id $SECRET_ARN \
  --query SecretString --output text | \
  jq -r '.private_key_openssh' > ~/.ssh/baiker-dev-key.pem

# Set proper permissions
chmod 600 ~/.ssh/baiker-dev-key.pem

# Verify key format
ssh-keygen -l -f ~/.ssh/baiker-dev-key.pem
```

### Step 2: Connect to Instances
```bash
# Connect to bastion host
ssh -i ~/.ssh/baiker-dev-key.pem ubuntu@$(terraform output -raw bastion_eip)

# Connect to main instance through bastion (recommended)
ssh -i ~/.ssh/baiker-dev-key.pem -J ubuntu@$(terraform output -raw bastion_eip) ubuntu@$(terraform output -raw main_instance_private_ip)

# Or connect directly to main instance
ssh -i ~/.ssh/baiker-dev-key.pem ubuntu@$(terraform output -raw main_eip)
```

### Alternative: One-time SSH Access
```bash
# For quick access without saving the key
aws secretsmanager get-secret-value --secret-id $(terraform output -raw private_key_secret_arn) \
  --query SecretString --output text | \
  jq -r '.private_key_openssh' | \
  ssh -i /dev/stdin ubuntu@$(terraform output -raw bastion_eip)
```

## Testing

To test this module:

```bash
# Initialize terraform
terraform init

# Plan the deployment
terraform plan

# Apply the configuration
terraform apply

# Retrieve and test SSH connectivity
SECRET_ARN=$(terraform output -raw private_key_secret_arn)
aws secretsmanager get-secret-value --secret-id $SECRET_ARN \
  --query SecretString --output text | \
  jq -r '.private_key_openssh' > ~/.ssh/baiker-test-key.pem
chmod 600 ~/.ssh/baiker-test-key.pem

# Test SSH connectivity
ssh -i ~/.ssh/baiker-test-key.pem ubuntu@$(terraform output -raw bastion_eip)
ssh -i ~/.ssh/baiker-test-key.pem ubuntu@$(terraform output -raw main_eip)

# Verify instances
aws ec2 describe-instances --instance-ids $(terraform output -raw bastion_instance_id) $(terraform output -raw main_instance_id)

# Check instance status
aws ec2 describe-instance-status --instance-ids $(terraform output -raw bastion_instance_id) $(terraform output -raw main_instance_id)

# Verify key pair
aws ec2 describe-key-pairs --key-names $(terraform output -raw key_pair_name)

# Clean up
rm ~/.ssh/baiker-test-key.pem
terraform destroy
```

## Security Considerations

- **Enhanced Security**: Private keys generated and stored in AWS Secrets Manager
- **No Local Key Exposure**: Keys never exist on local development machines
- **EBS Encryption**: All volumes encrypted by default
- **Network Security**: Security groups control access
- **Modern Cryptography**: ED25519 keys (more secure than RSA)
- **Secure Access**: Bastion host provides controlled access to private resources
- **Access Control**: IAM permissions required to retrieve private keys
- **User Data Security**: Scripts are base64 encoded

## Cost Optimization

- Bastion host uses t2.micro (eligible for free tier)
- Main instance uses t2.medium (cost-effective for development)
- GP3 storage provides better cost/performance ratio
- Elastic IPs ensure consistent access without additional charges when attached