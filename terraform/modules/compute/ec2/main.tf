# Generate ED25519 Private Key
resource "tls_private_key" "main" {
  algorithm = "ED25519"
}

# AWS Key Pair (using generated public key)
resource "aws_key_pair" "main" {
  key_name   = var.key_pair_name
  public_key = tls_private_key.main.public_key_openssh

  tags = merge(var.tags, {
    Name = var.key_pair_name
    Type = "ED25519"
  })
}

# Store Private Key in AWS Secrets Manager
resource "aws_secretsmanager_secret" "private_key" {
  name                    = "${var.key_pair_name}-private-key"
  description            = "ED25519 private key for ${var.key_pair_name}"
  recovery_window_in_days = 7

  tags = merge(var.tags, {
    Name = "${var.key_pair_name}-private-key"
  })
}

resource "aws_secretsmanager_secret_version" "private_key" {
  secret_id = aws_secretsmanager_secret.private_key.id
  secret_string = jsonencode({
    private_key_pem = tls_private_key.main.private_key_pem
    private_key_openssh = tls_private_key.main.private_key_openssh
    public_key_openssh = tls_private_key.main.public_key_openssh
    public_key_fingerprint = tls_private_key.main.public_key_fingerprint_sha256
  })
}

# Bastion Host Instance
resource "aws_instance" "bastion" {
  ami                    = var.ami_id
  instance_type          = var.bastion_instance_type
  key_name              = aws_key_pair.main.key_name
  subnet_id             = var.public_subnet_ids[0]
  vpc_security_group_ids = [var.bastion_security_group_id]

  associate_public_ip_address = true

  user_data = base64encode(<<-EOF
    #!/bin/bash
    apt update && apt upgrade -y && apt autoremove -y && apt install -y build-essential cmake make zip unzip tar wget curl vim
  EOF
  )

  root_block_device {
    volume_type = "gp3"
    volume_size = 20
    encrypted   = true
  }

  tags = merge(var.tags, {
    Name = "baiker-dev-ec2-bastion-host"
    Type = "Bastion"
  })
}

# Main EC2 Instance
resource "aws_instance" "main" {
  ami                    = var.ami_id
  instance_type          = var.main_instance_type
  key_name              = aws_key_pair.main.key_name
  subnet_id             = var.public_subnet_ids[0]  # Same subnet as bastion
  vpc_security_group_ids = [var.main_security_group_id]

  associate_public_ip_address = true

  user_data = base64encode(<<-EOF
    #!/bin/bash
    apt update && apt upgrade -y && apt autoremove -y && apt install -y build-essential cmake make zip unzip tar wget curl vim
    
    # Remove old Docker packages
    for pkg in docker.io docker-doc docker-compose docker-compose-v2 podman-docker containerd runc; do sudo apt-get remove $pkg; done
    
    # Add Docker's official GPG key
    sudo apt-get update
    sudo apt-get install ca-certificates curl
    sudo install -m 0755 -d /etc/apt/keyrings
    sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
    sudo chmod a+r /etc/apt/keyrings/docker.asc
    
    # Add the repository to Apt sources
    echo \
      "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
      $(. /etc/os-release && echo "${UBUNTU_CODENAME:-$VERSION_CODENAME}") stable" | \
      sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    sudo apt-get update
    sudo apt-get install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin -y
    usermod -aG docker $USER
  EOF
  )

  root_block_device {
    volume_type = "gp3"
    volume_size = 50
    encrypted   = true
  }

  tags = merge(var.tags, {
    Name = "baiker-dev-ec2"
    Type = "Main"
  })
}

# Elastic IP for Bastion Host (optional but recommended for static access)
resource "aws_eip" "bastion" {
  instance = aws_instance.bastion.id
  domain   = "vpc"

  tags = merge(var.tags, {
    Name = "baiker-dev-bastion-eip"
  })

  depends_on = [aws_instance.bastion]
}

# Elastic IP for Main Instance (optional)
resource "aws_eip" "main" {
  instance = aws_instance.main.id
  domain   = "vpc"

  tags = merge(var.tags, {
    Name = "baiker-dev-main-eip"
  })

  depends_on = [aws_instance.main]
}