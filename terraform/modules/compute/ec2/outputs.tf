output "key_pair_name" {
  description = "Name of the key pair"
  value       = aws_key_pair.main.key_name
}

output "key_pair_id" {
  description = "ID of the key pair"
  value       = aws_key_pair.main.id
}

output "private_key_secret_arn" {
  description = "ARN of the secret containing the private key"
  value       = aws_secretsmanager_secret.private_key.arn
}

output "private_key_secret_name" {
  description = "Name of the secret containing the private key"
  value       = aws_secretsmanager_secret.private_key.name
}

output "public_key_fingerprint" {
  description = "SHA256 fingerprint of the public key"
  value       = tls_private_key.main.public_key_fingerprint_sha256
}

output "bastion_instance_id" {
  description = "ID of the bastion instance"
  value       = aws_instance.bastion.id
}

output "bastion_instance_private_ip" {
  description = "Private IP of the bastion instance"
  value       = aws_instance.bastion.private_ip
}

output "bastion_instance_public_ip" {
  description = "Public IP of the bastion instance"
  value       = aws_instance.bastion.public_ip
}

output "bastion_eip" {
  description = "Elastic IP of the bastion instance"
  value       = aws_eip.bastion.public_ip
}

output "main_instance_id" {
  description = "ID of the main instance"
  value       = aws_instance.main.id
}

output "main_instance_private_ip" {
  description = "Private IP of the main instance"
  value       = aws_instance.main.private_ip
}

output "main_instance_public_ip" {
  description = "Public IP of the main instance"
  value       = aws_instance.main.public_ip
}

output "main_eip" {
  description = "Elastic IP of the main instance"
  value       = aws_eip.main.public_ip
}

output "instance_ids" {
  description = "Map of instance IDs"
  value = {
    bastion = aws_instance.bastion.id
    main    = aws_instance.main.id
  }
}