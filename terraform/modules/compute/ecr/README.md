# ECR Module

This module creates an AWS Elastic Container Registry (ECR) repository for storing Docker images.

## Features

- Creates ECR repository with configurable settings
- Enables image scanning on push for security
- Configurable image tag mutability
- Repository policies for controlled access
- Lifecycle policies to manage image retention
- AES256 encryption for image security

## Usage

```hcl
module "ecr" {
  source = "./modules/compute/ecr"

  repository_name      = "entanglement"
  image_tag_mutability = "MUTABLE"
  scan_on_push        = true
  encryption_type     = "AES256"

  tags = {
    Environment = "dev"
    Project     = "baiker"
    Namespace   = "dev"
  }
}
```

## Testing

To test this module:

```bash
# Initialize terraform
terraform init

# Plan the deployment
terraform plan

# Apply the configuration
terraform apply

# Get login token and login to ECR
aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin $(terraform output -raw repository_url)

# Build, tag and push a sample image
echo "FROM alpine:latest" > Dockerfile
echo "RUN echo 'Hello from Baiker ECR'" >> Dockerfile
docker build -t test-image .
docker tag test-image:latest $(terraform output -raw repository_url):latest
docker push $(terraform output -raw repository_url):latest

# Verify the image was pushed
aws ecr describe-images --repository-name entanglement

# Clean up
terraform destroy
```