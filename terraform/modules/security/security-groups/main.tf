# Security Group 1: Main EC2 Security Group
resource "aws_security_group" "main_ec2" {
  name        = "baiker-dev-sg"
  description = "Allow Port to EC2 using GPU"
  vpc_id      = var.vpc_id

  # SSH from bastion security group
  ingress {
    description     = "SSH from bastion"
    from_port       = 22
    to_port         = 22
    protocol        = "tcp"
    security_groups = [aws_security_group.ssh_bastion.id]
  }

  # Custom TCP 9999 from NLB security group
  ingress {
    description     = "Custom TCP 9999 from NLB"
    from_port       = 9999
    to_port         = 9999
    protocol        = "tcp"
    security_groups = [aws_security_group.nlb.id]
  }

  # Custom TCP 1986 from NLB security group
  ingress {
    description     = "Custom TCP 1986 from NLB"
    from_port       = 1986
    to_port         = 1986
    protocol        = "tcp"
    security_groups = [aws_security_group.nlb.id]
  }

  # Custom TCP 8003 from NLB security group
  ingress {
    description     = "Custom TCP 8003 from NLB"
    from_port       = 8003
    to_port         = 8003
    protocol        = "tcp"
    security_groups = [aws_security_group.nlb.id]
  }

  # Custom UDP 5000 from NLB security group
  ingress {
    description     = "Custom UDP 5000 from NLB"
    from_port       = 5000
    to_port         = 5000
    protocol        = "udp"
    security_groups = [aws_security_group.nlb.id]
  }

  # All outbound traffic
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.tags, {
    Name = "baiker-dev-sg"
  })
}

# Security Group 2: SSH Bastion Security Group
resource "aws_security_group" "ssh_bastion" {
  name        = "baiker-dev-sg-ssh-bastion"
  description = "Allow SSH access to developers"
  vpc_id      = var.vpc_id

  # SSH from specific IP
  ingress {
    description = "SSH from specific IP"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = [var.allowed_ssh_cidr]
  }

  # All outbound traffic
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.tags, {
    Name = "baiker-dev-sg-ssh-bastion"
  })
}

# Security Group 3: RDS PostgreSQL Security Group
resource "aws_security_group" "rds_postgres" {
  name        = "baiker-dev-sg-rds-postgre"
  description = "Allow connect to Postgres Database"
  vpc_id      = var.vpc_id

  # PostgreSQL port 5454 from specific IP
  ingress {
    description = "PostgreSQL from specific IP"
    from_port   = 5454
    to_port     = 5454
    protocol    = "tcp"
    cidr_blocks = [var.allowed_ssh_cidr]
  }

  # All outbound traffic
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.tags, {
    Name = "baiker-dev-sg-rds-postgre"
  })
}

# Security Group 4: Redis Security Group
resource "aws_security_group" "redis" {
  name        = "baiker-dev-redis-db"
  description = "Allow connect to MemoryDB Redis"
  vpc_id      = var.vpc_id

  # Redis port 6369 from specific IP
  ingress {
    description = "Redis from specific IP"
    from_port   = 6369
    to_port     = 6369
    protocol    = "tcp"
    cidr_blocks = [var.allowed_ssh_cidr]
  }

  # All outbound traffic
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.tags, {
    Name = "baiker-dev-redis-db"
  })
}

# Security Group 5: Network Load Balancer Security Group
resource "aws_security_group" "nlb" {
  name        = "baiker-dev-sg-nlb"
  description = "Allow Port for Network Load Balancer"
  vpc_id      = var.vpc_id

  # Custom TCP 4999 from anywhere
  ingress {
    description = "Custom TCP 4999 from anywhere"
    from_port   = 4999
    to_port     = 4999
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Custom TCP 9999 from anywhere
  ingress {
    description = "Custom TCP 9999 from anywhere"
    from_port   = 9999
    to_port     = 9999
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Custom TCP 1986 from anywhere
  ingress {
    description = "Custom TCP 1986 from anywhere"
    from_port   = 1986
    to_port     = 1986
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Custom TCP 8003 from anywhere
  ingress {
    description = "Custom TCP 8003 from anywhere"
    from_port   = 8003
    to_port     = 8003
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Custom UDP 5000 from anywhere
  ingress {
    description = "Custom UDP 5000 from anywhere"
    from_port   = 5000
    to_port     = 5000
    protocol    = "udp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # All outbound traffic
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.tags, {
    Name = "baiker-dev-sg-nlb"
  })
}