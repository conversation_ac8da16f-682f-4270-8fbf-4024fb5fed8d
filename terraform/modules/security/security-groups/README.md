# Security Groups Module

This module creates AWS Security Groups for the Baiker development environment.

## Features

Creates 5 security groups:
1. **Main EC2 Security Group** - For GPU-enabled EC2 instances
2. **SSH Bastion Security Group** - For bastion host access
3. **RDS PostgreSQL Security Group** - For database access
4. **Redis Security Group** - For MemoryDB Redis access
5. **Network Load Balancer Security Group** - For load balancer traffic

## Usage

```hcl
module "security_groups" {
  source = "./modules/security/security-groups"

  vpc_id           = module.vpc.vpc_id
  allowed_ssh_cidr = "*************/32"

  tags = {
    Environment = "dev"
    Project     = "baiker"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| vpc_id | ID of the VPC where security groups will be created | `string` | n/a | yes |
| allowed_ssh_cidr | CIDR block allowed for SSH access | `string` | `"*************/32"` | no |
| tags | A map of tags to assign to the resource | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| main_ec2_security_group_id | ID of the main EC2 security group |
| ssh_bastion_security_group_id | ID of the SSH bastion security group |
| rds_postgres_security_group_id | ID of the RDS PostgreSQL security group |
| redis_security_group_id | ID of the Redis security group |
| nlb_security_group_id | ID of the Network Load Balancer security group |
| security_group_ids | Map of all security group IDs |

## Security Group Details

### 1. Main EC2 Security Group (baiker-dev-sg)
**Inbound Rules:**
- SSH (22) from bastion security group
- TCP 9999 from NLB security group
- TCP 1986 from NLB security group
- TCP 8003 from NLB security group
- UDP 5000 from NLB security group

**Outbound Rules:**
- All traffic to anywhere

### 2. SSH Bastion Security Group (baiker-dev-sg-ssh-bastion)
**Inbound Rules:**
- SSH (22) from specific IP (*************/32)

**Outbound Rules:**
- All traffic to anywhere

### 3. RDS PostgreSQL Security Group (baiker-dev-sg-rds-postgre)
**Inbound Rules:**
- TCP 5454 (PostgreSQL) from specific IP (*************/32)

**Outbound Rules:**
- All traffic to anywhere

### 4. Redis Security Group (baiker-dev-redis-db)
**Inbound Rules:**
- TCP 6369 (Redis) from specific IP (*************/32)

**Outbound Rules:**
- All traffic to anywhere

### 5. Network Load Balancer Security Group (baiker-dev-sg-nlb)
**Inbound Rules:**
- TCP 4999 from anywhere
- TCP 9999 from anywhere
- TCP 1986 from anywhere
- TCP 8003 from anywhere
- UDP 5000 from anywhere

**Outbound Rules:**
- All traffic to anywhere

## Testing

To test this module:

```bash
# Initialize terraform
terraform init

# Plan the deployment
terraform plan

# Apply the configuration
terraform apply

# Verify security groups creation
aws ec2 describe-security-groups --group-names "baiker-dev-sg" "baiker-dev-sg-ssh-bastion" "baiker-dev-sg-rds-postgre" "baiker-dev-redis-db" "baiker-dev-sg-nlb"

# Test specific security group rules
aws ec2 describe-security-groups --group-ids <security-group-id> --query 'SecurityGroups[0].{InboundRules:IpPermissions,OutboundRules:IpPermissionsEgress}'

# Clean up
terraform destroy
```

## Security Considerations

- SSH access is restricted to a specific IP address
- Database access (PostgreSQL and Redis) is restricted to the same specific IP
- NLB security group allows public access as required for load balancing
- All security groups follow principle of least privilege
- Outbound traffic is allowed for necessary system operations