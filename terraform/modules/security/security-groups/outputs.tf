output "main_ec2_security_group_id" {
  description = "ID of the main EC2 security group"
  value       = aws_security_group.main_ec2.id
}

output "ssh_bastion_security_group_id" {
  description = "ID of the SSH bastion security group"
  value       = aws_security_group.ssh_bastion.id
}

output "rds_postgres_security_group_id" {
  description = "ID of the RDS PostgreSQL security group"
  value       = aws_security_group.rds_postgres.id
}

output "redis_security_group_id" {
  description = "ID of the Redis security group"
  value       = aws_security_group.redis.id
}

output "nlb_security_group_id" {
  description = "ID of the Network Load Balancer security group"
  value       = aws_security_group.nlb.id
}

output "security_group_ids" {
  description = "Map of all security group IDs"
  value = {
    main_ec2    = aws_security_group.main_ec2.id
    ssh_bastion = aws_security_group.ssh_bastion.id
    rds_postgres = aws_security_group.rds_postgres.id
    redis       = aws_security_group.redis.id
    nlb         = aws_security_group.nlb.id
  }
}