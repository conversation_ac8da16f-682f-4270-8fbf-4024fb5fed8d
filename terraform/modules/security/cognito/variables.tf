variable "user_pool_name" {
  description = "Name of the Cognito User Pool"
  type        = string
  default     = "baiker-dev-user-pool"
}

variable "client_name" {
  description = "Name of the Cognito User Pool Client"
  type        = string
  default     = "baiker-dev-mobile-client"
}

variable "identity_pool_name" {
  description = "Name of the Cognito Identity Pool"
  type        = string
  default     = "baiker-dev-identity-pool"
}

variable "tags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
  default     = {}
}