# Test configuration for VPC module
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# Configure the AWS Provider
provider "aws" {
  region = "ap-northeast-1"
}

# Test the VPC module
module "vpc" {
  source = "../"

  vpc_name               = "baiker-dev-vpc"
  vpc_cidr              = "10.0.0.0/16"
  availability_zones    = ["ap-northeast-1a", "ap-northeast-1b"]
  public_subnet_cidrs   = ["10.0.0.0/20", "*********/20"]
  private_subnet_cidrs  = ["*********/20", "*********/20", "*********/20", "*********/20"]
  enable_nat_gateway    = true
  single_nat_gateway    = true
  enable_dns_hostnames  = true
  enable_dns_support    = true

  tags = {
    Environment = "dev"
    Project     = "baiker"
    Terraform   = "true"
  }
}

# Output the VPC details for verification
output "vpc_id" {
  description = "ID of the VPC"
  value       = module.vpc.vpc_id
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = module.vpc.vpc_cidr_block
}

output "public_subnet_ids" {
  description = "List of IDs of the public subnets"
  value       = module.vpc.public_subnet_ids
}

output "private_subnet_ids" {
  description = "List of IDs of the private subnets"
  value       = module.vpc.private_subnet_ids
}

output "nat_gateway_ids" {
  description = "List of IDs of the NAT Gateways"
  value       = module.vpc.nat_gateway_ids
}

output "internet_gateway_id" {
  description = "ID of the Internet Gateway"
  value       = module.vpc.internet_gateway_id
}
