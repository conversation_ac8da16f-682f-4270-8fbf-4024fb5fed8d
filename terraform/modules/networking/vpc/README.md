# VPC Module

This module creates an AWS VPC with public and private subnets, NAT Gateway, and Internet Gateway.

## Features

- Creates VPC with DNS hostnames and DNS resolution enabled
- Creates 2 public subnets across 2 availability zones
- Creates 4 private subnets across 2 availability zones  
- Creates Internet Gateway for public subnet access
- Creates single NAT Gateway in first public subnet for private subnet internet access
- Creates route tables and associations
- Configures default Network ACL to allow all traffic

## Usage

```hcl
module "vpc" {
  source = "./modules/networking/vpc"

  vpc_name               = "baiker-dev-vpc"
  vpc_cidr              = "10.0.0.0/16"
  availability_zones    = ["ap-northeast-1a", "ap-northeast-1b"]
  public_subnet_cidrs   = ["10.0.0.0/20", "*********/20"]
  private_subnet_cidrs  = ["*********/20", "*********/20", "*********/20", "*********/20"]
  enable_nat_gateway    = true
  single_nat_gateway    = true
  enable_dns_hostnames  = true
  enable_dns_support    = true

  tags = {
    Environment = "dev"
    Project     = "baiker"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| vpc_name | Name of the VPC | `string` | n/a | yes |
| vpc_cidr | CIDR block for VPC | `string` | `"10.0.0.0/16"` | no |
| availability_zones | Availability zones | `list(string)` | `["ap-northeast-1a", "ap-northeast-1b"]` | no |
| public_subnet_cidrs | CIDR blocks for public subnets | `list(string)` | `["10.0.0.0/20", "*********/20"]` | no |
| private_subnet_cidrs | CIDR blocks for private subnets | `list(string)` | `["*********/20", "*********/20", "*********/20", "*********/20"]` | no |
| enable_nat_gateway | Should be true to provision NAT Gateways | `bool` | `true` | no |
| single_nat_gateway | Should be true to provision a single shared NAT Gateway | `bool` | `true` | no |
| enable_dns_hostnames | Should be true to enable DNS hostnames in the VPC | `bool` | `true` | no |
| enable_dns_support | Should be true to enable DNS support in the VPC | `bool` | `true` | no |
| tags | A map of tags to assign to the resource | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| vpc_id | ID of the VPC |
| vpc_arn | ARN of the VPC |
| vpc_cidr_block | CIDR block of the VPC |
| internet_gateway_id | ID of the Internet Gateway |
| public_subnet_ids | List of IDs of the public subnets |
| private_subnet_ids | List of IDs of the private subnets |
| public_subnet_cidrs | List of CIDR blocks of the public subnets |
| private_subnet_cidrs | List of CIDR blocks of the private subnets |
| nat_gateway_ids | List of IDs of the NAT Gateways |
| nat_gateway_public_ips | List of public Elastic IPs for NAT Gateway |
| public_route_table_id | ID of the public route table |
| private_route_table_ids | List of IDs of the private route tables |
| default_network_acl_id | ID of the default Network ACL |
| default_route_table_id | ID of the default route table |

## Resources Created

- 1 VPC
- 1 Internet Gateway
- 2 Public Subnets
- 4 Private Subnets
- 1 NAT Gateway
- 1 Elastic IP for NAT Gateway
- 1 Public Route Table
- 1 Private Route Table
- Route Table Associations
- Default Network ACL (configured)
- Default Route Table (tagged)

## Testing

To test this module:

```bash
# Initialize terraform
terraform init

# Plan the deployment
terraform plan

# Apply the configuration
terraform apply

# Verify VPC creation
aws ec2 describe-vpcs --vpc-ids <vpc-id>

# Verify subnets
aws ec2 describe-subnets --filters "Name=vpc-id,Values=<vpc-id>"

# Verify NAT Gateway
aws ec2 describe-nat-gateways --filter "Name=vpc-id,Values=<vpc-id>"

# Clean up
terraform destroy
```