variable "vpc_name" {
  description = "Name of the VPC"
  type        = string
}

variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "enable_dns_hostnames" {
  description = "Should be true to enable DNS hostnames in the VPC"
  type        = bool
  default     = true
}

variable "enable_dns_support" {
  description = "Should be true to enable DNS support in the VPC"
  type        = bool
  default     = true
}

variable "public_subnets" {
  description = "Map of public subnet configurations"
  type = map(object({
    name              = string
    cidr_block        = string
    availability_zone = string
  }))
  default = {}
}

variable "private_subnets" {
  description = "Map of private subnet configurations"
  type = map(object({
    name              = string
    cidr_block        = string
    availability_zone = string
    route_table_key   = string
  }))
  default = {}
}

variable "private_route_tables" {
  description = "Map of private route table configurations"
  type = map(object({
    name = string
  }))
  default = {}
}

variable "enable_nat_gateway" {
  description = "Should be true if you want to provision NAT Gateway"
  type        = bool
  default     = true
}

variable "nat_gateway_subnet_key" {
  description = "The key of the public subnet to place the NAT Gateway in"
  type        = string
  default     = ""
}

variable "interface_endpoints" {
  description = "Map of VPC interface endpoint configurations"
  type = map(object({
    name                = string
    service_name        = string
    subnet_keys         = list(string)
    security_group_ids  = list(string)
    policy              = string
    private_dns_enabled = bool
  }))
  default = {}
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
}