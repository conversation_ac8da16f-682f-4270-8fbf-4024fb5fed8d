# Load Balancer Module

This module creates an AWS Network Load Balancer (NLB) with target groups and listeners for the Baiker development environment.

## Features

- Creates internal Network Load Balancer in private subnets
- Provisions 3 target groups for different services:
  - HumanAI (TCP 9999)
  - SRS (TCP 1986)  
  - SRS-Stream (UDP 5000)
- Configures listeners for each service
- Registers EC2 instances as targets
- Implements health checks for all target groups

## Usage

```hcl
module "load_balancer" {
  source = "./modules/networking/load-balancer"

  vpc_id                = module.vpc.vpc_id
  private_subnet_ids    = module.vpc.private_subnet_ids
  nlb_security_group_id = module.security_groups.nlb_security_group_id
  target_instance_ids   = [module.ec2.main_instance_id]

  tags = {
    Environment = "dev"
    Project     = "baiker"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| vpc_id | ID of the VPC | `string` | n/a | yes |
| private_subnet_ids | List of private subnet IDs for NLB | `list(string)` | n/a | yes |
| nlb_security_group_id | Security group ID for Network Load Balancer | `string` | n/a | yes |
| target_instance_ids | List of EC2 instance IDs to register as targets | `list(string)` | n/a | yes |
| tags | A map of tags to assign to the resource | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| nlb_id | ID of the Network Load Balancer |
| nlb_arn | ARN of the Network Load Balancer |
| nlb_dns_name | DNS name of the Network Load Balancer |
| nlb_zone_id | Zone ID of the Network Load Balancer |
| target_group_arns | ARNs of the target groups |
| target_group_ids | IDs of the target groups |
| listener_arns | ARNs of the listeners |
| humanai_target_group_arn | ARN of the HumanAI target group |
| srs_target_group_arn | ARN of the SRS target group |
| srs_stream_target_group_arn | ARN of the SRS Stream target group |

## Architecture Details

### Network Load Balancer
- **Name**: baiker-dev-nlb
- **Type**: Internal (private)
- **IP Address Type**: IPv4
- **Subnets**: First 2 private subnets (baiker-dev-private-subnet-1, baiker-dev-private-subnet-2)
- **Deletion Protection**: Disabled (for development)

### Target Groups

#### 1. HumanAI Target Group (baiker-dev-tg-humanai)
- **Port**: 9999
- **Protocol**: TCP
- **Health Check**: TCP on port 9999
- **Health Check Settings**:
  - Healthy Threshold: 2
  - Unhealthy Threshold: 2
  - Timeout: 5 seconds
  - Interval: 10 seconds

#### 2. SRS Target Group (baiker-dev-tg-srs)
- **Port**: 1986
- **Protocol**: TCP
- **Health Check**: TCP on port 1986
- **Health Check Settings**:
  - Healthy Threshold: 2
  - Unhealthy Threshold: 2
  - Timeout: 5 seconds
  - Interval: 10 seconds

#### 3. SRS-Stream Target Group (baiker-dev-tg-srs-stream)
- **Port**: 5000
- **Protocol**: UDP
- **Health Check**: TCP on port 5000 (UDP target groups use TCP health checks)
- **Health Check Settings**:
  - Healthy Threshold: 2
  - Unhealthy Threshold: 2
  - Timeout: 5 seconds
  - Interval: 10 seconds

### Listeners

- **HumanAI Listener**: Port 9999 TCP → Forward to HumanAI target group
- **SRS Listener**: Port 1986 TCP → Forward to SRS target group
- **SRS-Stream Listener**: Port 5000 UDP → Forward to SRS-Stream target group

## Service Endpoints

After deployment, services will be accessible through the NLB:

```bash
# Get NLB DNS name
NLB_DNS=$(terraform output -raw nlb_dns_name)

# Service endpoints
# HumanAI Service
telnet $NLB_DNS 9999

# SRS Service
telnet $NLB_DNS 1986

# SRS-Stream Service (UDP)
nc -u $NLB_DNS 5000
```

## Testing

To test this module:

```bash
# Initialize terraform
terraform init

# Plan the deployment
terraform plan

# Apply the configuration
terraform apply

# Verify NLB creation
aws elbv2 describe-load-balancers --names "baiker-dev-nlb"

# Verify target groups
aws elbv2 describe-target-groups --names "baiker-dev-tg-humanai" "baiker-dev-tg-srs" "baiker-dev-tg-srs-stream"

# Check target health
aws elbv2 describe-target-health --target-group-arn $(terraform output -raw humanai_target_group_arn)
aws elbv2 describe-target-health --target-group-arn $(terraform output -raw srs_target_group_arn)
aws elbv2 describe-target-health --target-group-arn $(terraform output -raw srs_stream_target_group_arn)

# Test connectivity (from within VPC)
telnet $(terraform output -raw nlb_dns_name) 9999
telnet $(terraform output -raw nlb_dns_name) 1986

# Clean up
terraform destroy
```

## Health Check Considerations

- All target groups use TCP health checks for reliability
- Health check timeouts and intervals are configured for development environments
- Targets must respond on the specified ports to be considered healthy
- Health checks run from the load balancer nodes in each subnet

## Security

- NLB is internal-only (not internet-facing)
- Security groups control access to the load balancer
- Target registration is automated through Terraform
- Health checks ensure only healthy targets receive traffic

## Cost Optimization

- Single NLB handles multiple services (cost-effective)
- Internal NLB reduces data transfer costs
- Health check intervals are optimized for responsiveness vs. cost
- Target groups automatically handle unhealthy instances