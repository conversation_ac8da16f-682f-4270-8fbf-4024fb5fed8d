output "nlb_id" {
  description = "ID of the Network Load Balancer"
  value       = aws_lb.main.id
}

output "nlb_arn" {
  description = "ARN of the Network Load Balancer"
  value       = aws_lb.main.arn
}

output "nlb_dns_name" {
  description = "DNS name of the Network Load Balancer"
  value       = aws_lb.main.dns_name
}

output "nlb_zone_id" {
  description = "Zone ID of the Network Load Balancer"
  value       = aws_lb.main.zone_id
}

output "target_group_arns" {
  description = "ARNs of the target groups"
  value = {
    humanai    = aws_lb_target_group.humanai.arn
    srs        = aws_lb_target_group.srs.arn
    srs_stream = aws_lb_target_group.srs_stream.arn
  }
}

output "target_group_ids" {
  description = "IDs of the target groups"
  value = {
    humanai    = aws_lb_target_group.humanai.id
    srs        = aws_lb_target_group.srs.id
    srs_stream = aws_lb_target_group.srs_stream.id
  }
}

output "listener_arns" {
  description = "ARNs of the listeners"
  value = {
    humanai    = aws_lb_listener.humanai.arn
    srs        = aws_lb_listener.srs.arn
    srs_stream = aws_lb_listener.srs_stream.arn
  }
}

output "humanai_target_group_arn" {
  description = "ARN of the HumanAI target group"
  value       = aws_lb_target_group.humanai.arn
}

output "srs_target_group_arn" {
  description = "ARN of the SRS target group"
  value       = aws_lb_target_group.srs.arn
}

output "srs_stream_target_group_arn" {
  description = "ARN of the SRS Stream target group"
  value       = aws_lb_target_group.srs_stream.arn
}