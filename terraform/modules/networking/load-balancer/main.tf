# Target Group for HumanAI (Port 9999)
resource "aws_lb_target_group" "humanai" {
  name     = "baiker-dev-tg-humanai"
  port     = 9999
  protocol = "TCP"
  vpc_id   = var.vpc_id

  target_type = "instance"

  health_check {
    enabled             = true
    healthy_threshold   = 2
    unhealthy_threshold = 2
    timeout             = 5
    interval            = 10
    port                = "traffic-port"
    protocol            = "TCP"
  }

  tags = merge(var.tags, {
    Name    = "baiker-dev-tg-humanai"
    Service = "HumanAI"
  })
}

# Target Group for SRS (Port 1986)
resource "aws_lb_target_group" "srs" {
  name     = "baiker-dev-tg-srs"
  port     = 1986
  protocol = "TCP"
  vpc_id   = var.vpc_id

  target_type = "instance"

  health_check {
    enabled             = true
    healthy_threshold   = 2
    unhealthy_threshold = 2
    timeout             = 5
    interval            = 10
    port                = "traffic-port"
    protocol            = "TCP"
  }

  tags = merge(var.tags, {
    Name    = "baiker-dev-tg-srs"
    Service = "SRS"
  })
}

# Target Group for SRS-Stream (Port 5000 UDP)
resource "aws_lb_target_group" "srs_stream" {
  name     = "baiker-dev-tg-srs-stream"
  port     = 5000
  protocol = "UDP"
  vpc_id   = var.vpc_id

  target_type = "instance"

  health_check {
    enabled             = true
    healthy_threshold   = 2
    unhealthy_threshold = 2
    timeout             = 5
    interval            = 10
    port                = 5000
    protocol            = "TCP"  # Health check uses TCP even for UDP target groups
  }

  tags = merge(var.tags, {
    Name    = "baiker-dev-tg-srs-stream"
    Service = "SRS-Stream"
  })
}

# Network Load Balancer
resource "aws_lb" "main" {
  name               = "baiker-dev-nlb"
  internal           = true
  load_balancer_type = "network"
  ip_address_type    = "ipv4"
  
  # Use only the first 2 private subnets (subnet-1 and subnet-2)
  subnets = slice(var.private_subnet_ids, 0, 2)

  enable_deletion_protection = false

  tags = merge(var.tags, {
    Name = "baiker-dev-nlb"
    Type = "Network"
  })
}

# Listener for HumanAI (Port 9999)
resource "aws_lb_listener" "humanai" {
  load_balancer_arn = aws_lb.main.arn
  port              = "9999"
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.humanai.arn
  }

  tags = merge(var.tags, {
    Name    = "baiker-dev-nlb-listener-humanai"
    Service = "HumanAI"
  })
}

# Listener for SRS (Port 1986)
resource "aws_lb_listener" "srs" {
  load_balancer_arn = aws_lb.main.arn
  port              = "1986"
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.srs.arn
  }

  tags = merge(var.tags, {
    Name    = "baiker-dev-nlb-listener-srs"
    Service = "SRS"
  })
}

# Listener for SRS-Stream (Port 5000 UDP)
resource "aws_lb_listener" "srs_stream" {
  load_balancer_arn = aws_lb.main.arn
  port              = "5000"
  protocol          = "UDP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.srs_stream.arn
  }

  tags = merge(var.tags, {
    Name    = "baiker-dev-nlb-listener-srs-stream"
    Service = "SRS-Stream"
  })
}

# Target Group Attachments - Register EC2 instances
resource "aws_lb_target_group_attachment" "humanai" {
  count            = length(var.target_instance_ids)
  target_group_arn = aws_lb_target_group.humanai.arn
  target_id        = var.target_instance_ids[count.index]
  port             = 9999
}

resource "aws_lb_target_group_attachment" "srs" {
  count            = length(var.target_instance_ids)
  target_group_arn = aws_lb_target_group.srs.arn
  target_id        = var.target_instance_ids[count.index]
  port             = 1986
}

resource "aws_lb_target_group_attachment" "srs_stream" {
  count            = length(var.target_instance_ids)
  target_group_arn = aws_lb_target_group.srs_stream.arn
  target_id        = var.target_instance_ids[count.index]
  port             = 5000
}