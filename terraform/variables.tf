# General Variables
variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "ap-northeast-1"
}

variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "baiker"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "dev"
}

variable "default_tags" {
  description = "Default tags to apply to all resources"
  type        = map(string)
  default = {
    Project     = "baiker"
    Environment = "dev"
    ManagedBy   = "terraform"
  }
}

# VPC Variables
variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "availability_zones" {
  description = "Availability zones"
  type        = list(string)
  default     = ["ap-northeast-1a", "ap-northeast-1b"]
}

variable "public_subnet_cidrs" {
  description = "CIDR blocks for public subnets"
  type        = list(string)
  default     = ["10.0.0.0/20", "*********/20"]
}

variable "private_subnet_cidrs" {
  description = "CIDR blocks for private subnets"
  type        = list(string)
  default     = ["*********/20", "*********/20", "*********/20", "*********/20"]
}

# Security Variables
variable "allowed_ssh_cidr" {
  description = "CIDR block allowed for SSH access"
  type        = string
  default     = "*************/32"
}

# EC2 Variables
variable "ami_id" {
  description = "AMI ID for EC2 instances"
  type        = string
  default     = "ami-054400ced365b82a0"  # Ubuntu 24.04 LTS in ap-northeast-1
}

variable "bastion_instance_type" {
  description = "Instance type for bastion host"
  type        = string
  default     = "t2.micro"
}

variable "main_instance_type" {
  description = "Instance type for main EC2 instance"
  type        = string
  default     = "t2.medium"
}

# RDS Variables
variable "db_name" {
  description = "Name of the database"
  type        = string
  default     = "baiker_dev"
}

variable "db_username" {
  description = "Username for the master DB user"
  type        = string
  default     = "baiker"
}

variable "db_port" {
  description = "Port for the RDS instance"
  type        = number
  default     = 5454
}

variable "postgres_engine_version" {
  description = "PostgreSQL engine version"
  type        = string
  default     = "17.4"
}

variable "rds_instance_class" {
  description = "RDS instance class"
  type        = string
  default     = "db.m7g.large"
}

variable "rds_allocated_storage" {
  description = "Initial storage allocation"
  type        = number
  default     = 50
}

variable "rds_max_allocated_storage" {
  description = "Maximum storage allocation for auto-scaling"
  type        = number
  default     = 200
}

# ECR Variables
variable "ecr_repository_name" {
  description = "Name of the ECR repository"
  type        = string
  default     = "entanglement"
}

# Redis Variables
variable "redis_node_type" {
  description = "Node type for MemoryDB cluster"
  type        = string
  default     = "db.r7g.large"
}