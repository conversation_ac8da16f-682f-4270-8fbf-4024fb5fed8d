# VPC Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = module.vpc.vpc_id
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = module.vpc.vpc_cidr_block
}

output "public_subnet_ids" {
  description = "List of IDs of the public subnets"
  value       = module.vpc.public_subnet_ids
}

output "private_subnet_ids" {
  description = "List of IDs of the private subnets"
  value       = module.vpc.private_subnet_ids
}

# EC2 Outputs
output "bastion_instance_id" {
  description = "ID of the bastion instance"
  value       = module.ec2.bastion_instance_id
}

output "bastion_eip" {
  description = "Elastic IP of the bastion instance"
  value       = module.ec2.bastion_eip
}

output "main_instance_id" {
  description = "ID of the main instance"
  value       = module.ec2.main_instance_id
}

output "main_eip" {
  description = "Elastic IP of the main instance"
  value       = module.ec2.main_eip
}

output "private_key_secret_arn" {
  description = "ARN of the secret containing the SSH private key"
  value       = module.ec2.private_key_secret_arn
}

output "private_key_secret_name" {
  description = "Name of the secret containing the SSH private key"
  value       = module.ec2.private_key_secret_name
}

output "public_key_fingerprint" {
  description = "SHA256 fingerprint of the SSH public key"
  value       = module.ec2.public_key_fingerprint
}

# Load Balancer Outputs
output "nlb_dns_name" {
  description = "DNS name of the Network Load Balancer"
  value       = module.load_balancer.nlb_dns_name
}

output "nlb_arn" {
  description = "ARN of the Network Load Balancer"
  value       = module.load_balancer.nlb_arn
}

# RDS Outputs
output "rds_endpoint" {
  description = "RDS instance endpoint"
  value       = module.rds.db_instance_endpoint
}

output "rds_port" {
  description = "RDS instance port"
  value       = module.rds.db_instance_port
}

output "rds_secret_arn" {
  description = "Secrets Manager secret ARN containing database credentials"
  value       = module.rds.secret_arn
}

# ECR Outputs
output "ecr_repository_url" {
  description = "The URL of the repository"
  value       = module.ecr.repository_url
}

output "ecr_repository_arn" {
  description = "Full ARN of the repository"
  value       = module.ecr.repository_arn
}

# Cognito Outputs
output "cognito_user_pool_id" {
  description = "ID of the Cognito User Pool"
  value       = module.cognito.user_pool_id
}

output "cognito_user_pool_client_id" {
  description = "ID of the Cognito User Pool Client"
  value       = module.cognito.user_pool_client_id
}

output "cognito_identity_pool_id" {
  description = "ID of the Cognito Identity Pool"
  value       = module.cognito.identity_pool_id
}

# S3 Outputs
output "terraform_state_bucket_id" {
  description = "ID of the Terraform state bucket"
  value       = module.s3.terraform_state_bucket_id
}

output "raw_data_bucket_id" {
  description = "ID of the raw data bucket"
  value       = module.s3.raw_data_bucket_id
}

# Redis Outputs
output "redis_cluster_endpoint" {
  description = "DNS hostname of the cluster configuration endpoint"
  value       = module.redis.cluster_endpoint
}

output "redis_cluster_port" {
  description = "Port number of the cluster"
  value       = module.redis.cluster_port
}