include "root" {
  path = find_in_parent_folders()
}

terraform {
  source = "../../../../../terraform/modules/security/security-groups"
}

dependency "vpc" {
  config_path = "../vpc"
  
  mock_outputs = {
    vpc_id = "vpc-mock-id"
  }
  mock_outputs_allowed_terraform_commands = ["validate", "plan"]
}

inputs = {
  vpc_id = dependency.vpc.outputs.vpc_id
  
  allowed_ssh_cidr = "103.199.7.223/32"
  
  tags = {
    Environment = "dev"
    Project     = "baiker"
    ManagedBy   = "terraform"
  }
}