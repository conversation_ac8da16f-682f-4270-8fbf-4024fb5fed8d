include "root" {
  path = find_in_parent_folders()
}

terraform {
  source = "../../../../../terraform/modules/networking/vpc"
}

inputs = {
  vpc_name = "baiker-dev-vpc"
  vpc_cidr = "10.0.0.0/16"
  
  enable_dns_hostnames = true
  enable_dns_support   = true
  
  public_subnets = {
    "app-public" = {
      name              = "baiker-app-pl-sn"
      cidr_block        = "10.0.0.0/20"
      availability_zone = "ap-northeast-1a"
    }
    "web-public" = {
      name              = "baiker-web-pl-sn"
      cidr_block        = "*********/20"
      availability_zone = "ap-northeast-1c"
    }
  }
  
  private_subnets = {
    "web-private" = {
      name              = "baiker-web-sn"
      cidr_block        = "*********/20"
      availability_zone = "ap-northeast-1a"
      route_table_key   = "web-rt"
    }
    "app-private" = {
      name              = "baiker-app-sn"
      cidr_block        = "*********/20"
      availability_zone = "ap-northeast-1c"
      route_table_key   = "app-rt"
    }
    "rds-private" = {
      name              = "baiker-rds-sn"
      cidr_block        = "*********/20"
      availability_zone = "ap-northeast-1a"
      route_table_key   = "db-rt"
    }
    "redis-private" = {
      name              = "baiker-redis-sn"
      cidr_block        = "10.0.80.0/20"
      availability_zone = "ap-northeast-1c"
      route_table_key   = "db-rt"
    }
  }
  
  private_route_tables = {
    "web-rt" = {
      name = "baiker-web-sn-rtb"
    }
    "app-rt" = {
      name = "baiker-app-sn-rtb"
    }
    "db-rt" = {
      name = "baiker-db-sn-rtb"
    }
  }
  
  enable_nat_gateway = true
  nat_gateway_subnet_key = "app-public"
  
  interface_endpoints = {}
  
  tags = {
    Environment = "dev"
    Project     = "baiker"
    ManagedBy   = "terraform"
  }
}