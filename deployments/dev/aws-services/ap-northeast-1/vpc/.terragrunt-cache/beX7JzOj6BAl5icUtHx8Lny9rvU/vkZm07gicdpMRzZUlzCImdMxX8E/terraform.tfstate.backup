{"version": 4, "terraform_version": "1.12.1", "serial": 70, "lineage": "ecdcbe01-848f-07f3-e8e8-43c5b684777e", "outputs": {"default_network_acl_id": {"value": "acl-0a55ac3a764cf7535", "type": "string"}, "default_route_table_id": {"value": "rtb-0b45f23c8032c7203", "type": "string"}, "internet_gateway_id": {"value": "igw-07c6bc8adfff674bf", "type": "string"}, "nat_gateway_ids": {"value": ["nat-05152038aac28af1f"], "type": ["tuple", ["string"]]}, "nat_gateway_public_ips": {"value": ["*************"], "type": ["tuple", ["string"]]}, "private_route_table_ids": {"value": ["rtb-0905b4fe894f1ec7c", "rtb-0a5849b6d2df8d478", "rtb-0b24bc70f06a8c715"], "type": ["tuple", ["string", "string", "string"]]}, "private_route_tables": {"value": {"app-rt": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:route-table/rtb-0905b4fe894f1ec7c", "id": "rtb-0905b4fe894f1ec7c"}, "db-rt": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:route-table/rtb-0a5849b6d2df8d478", "id": "rtb-0a5849b6d2df8d478"}, "web-rt": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:route-table/rtb-0b24bc70f06a8c715", "id": "rtb-0b24bc70f06a8c715"}}, "type": ["object", {"app-rt": ["object", {"arn": "string", "id": "string"}], "db-rt": ["object", {"arn": "string", "id": "string"}], "web-rt": ["object", {"arn": "string", "id": "string"}]}]}, "private_subnet_cidrs": {"value": ["*********/20", "*********/20", "*********/20", "*********/20"], "type": ["tuple", ["string", "string", "string", "string"]]}, "private_subnet_ids": {"value": ["subnet-0f9cee2d8eef72cae", "subnet-007c248f2c1c1132c", "subnet-00cff853458dd9635", "subnet-0305f9862878c29e5"], "type": ["tuple", ["string", "string", "string", "string"]]}, "private_subnets": {"value": {"app-private": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:subnet/subnet-0f9cee2d8eef72cae", "availability_zone": "ap-northeast-1c", "cidr_block": "*********/20", "id": "subnet-0f9cee2d8eef72cae"}, "rds-private": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:subnet/subnet-007c248f2c1c1132c", "availability_zone": "ap-northeast-1a", "cidr_block": "*********/20", "id": "subnet-007c248f2c1c1132c"}, "redis-private": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:subnet/subnet-00cff853458dd9635", "availability_zone": "ap-northeast-1c", "cidr_block": "*********/20", "id": "subnet-00cff853458dd9635"}, "web-private": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:subnet/subnet-0305f9862878c29e5", "availability_zone": "ap-northeast-1a", "cidr_block": "*********/20", "id": "subnet-0305f9862878c29e5"}}, "type": ["object", {"app-private": ["object", {"arn": "string", "availability_zone": "string", "cidr_block": "string", "id": "string"}], "rds-private": ["object", {"arn": "string", "availability_zone": "string", "cidr_block": "string", "id": "string"}], "redis-private": ["object", {"arn": "string", "availability_zone": "string", "cidr_block": "string", "id": "string"}], "web-private": ["object", {"arn": "string", "availability_zone": "string", "cidr_block": "string", "id": "string"}]}]}, "public_route_table_id": {"value": "rtb-02c2726a96806e1b2", "type": "string"}, "public_subnet_cidrs": {"value": ["10.0.0.0/20", "*********/20"], "type": ["tuple", ["string", "string"]]}, "public_subnet_ids": {"value": ["subnet-0d451afced7d6f0c0", "subnet-008286c9b916e4388"], "type": ["tuple", ["string", "string"]]}, "public_subnets": {"value": {"app-public": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:subnet/subnet-0d451afced7d6f0c0", "availability_zone": "ap-northeast-1a", "cidr_block": "10.0.0.0/20", "id": "subnet-0d451afced7d6f0c0"}, "web-public": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:subnet/subnet-008286c9b916e4388", "availability_zone": "ap-northeast-1c", "cidr_block": "*********/20", "id": "subnet-008286c9b916e4388"}}, "type": ["object", {"app-public": ["object", {"arn": "string", "availability_zone": "string", "cidr_block": "string", "id": "string"}], "web-public": ["object", {"arn": "string", "availability_zone": "string", "cidr_block": "string", "id": "string"}]}]}, "vpc_arn": {"value": "arn:aws:ec2:ap-northeast-1:314146329201:vpc/vpc-0ba8398aabd49f856", "type": "string"}, "vpc_cidr_block": {"value": "10.0.0.0/16", "type": "string"}, "vpc_endpoint_ids": {"value": {}, "type": ["object", {}]}, "vpc_id": {"value": "vpc-0ba8398aabd49f856", "type": "string"}}, "resources": [{"mode": "managed", "type": "aws_default_network_acl", "name": "default", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:network-acl/acl-0a55ac3a764cf7535", "default_network_acl_id": "acl-0a55ac3a764cf7535", "egress": [{"action": "allow", "cidr_block": "0.0.0.0/0", "from_port": 0, "icmp_code": 0, "icmp_type": 0, "ipv6_cidr_block": "", "protocol": "-1", "rule_no": 100, "to_port": 0}], "id": "acl-0a55ac3a764cf7535", "ingress": [{"action": "allow", "cidr_block": "0.0.0.0/0", "from_port": 0, "icmp_code": 0, "icmp_type": 0, "ipv6_cidr_block": "", "protocol": "-1", "rule_no": 100, "to_port": 0}], "owner_id": "314146329201", "region": "ap-northeast-1", "subnet_ids": [], "tags": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-dev-vpc-default-nacl", "Project": "baiker"}, "tags_all": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-dev-vpc-default-nacl", "Project": "baiker"}, "vpc_id": "vpc-0ba8398aabd49f856"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_default_route_table", "name": "default", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:route-table/rtb-0b45f23c8032c7203", "default_route_table_id": "rtb-0b45f23c8032c7203", "id": "rtb-0b45f23c8032c7203", "owner_id": "314146329201", "propagating_vgws": [], "region": "ap-northeast-1", "route": [], "tags": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-dev-vpc-default-rt", "Project": "baiker"}, "tags_all": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-dev-vpc-default-rt", "Project": "baiker"}, "timeouts": null, "vpc_id": "vpc-0ba8398aabd49f856"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwfX0=", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_eip", "name": "nat", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"address": null, "allocation_id": "eipalloc-03dab13d5af1f1660", "arn": "arn:aws:ec2:ap-northeast-1:314146329201:elastic-ip/eipalloc-03dab13d5af1f1660", "associate_with_private_ip": null, "association_id": "", "carrier_ip": "", "customer_owned_ip": "", "customer_owned_ipv4_pool": "", "domain": "vpc", "id": "eipalloc-03dab13d5af1f1660", "instance": "", "ipam_pool_id": null, "network_border_group": "ap-northeast-1", "network_interface": "", "private_dns": null, "private_ip": "", "ptr_record": "", "public_dns": "ec2-18-180-96-235.ap-northeast-1.compute.amazonaws.com", "public_ip": "*************", "public_ipv4_pool": "amazon", "region": "ap-northeast-1", "tags": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-dev-vpc-eip", "Project": "baiker"}, "tags_all": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-dev-vpc-eip", "Project": "baiker"}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiZGVsZXRlIjoxODAwMDAwMDAwMDAsInJlYWQiOjkwMDAwMDAwMDAwMCwidXBkYXRlIjozMDAwMDAwMDAwMDB9fQ==", "dependencies": ["aws_internet_gateway.main", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_internet_gateway", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:internet-gateway/igw-07c6bc8adfff674bf", "id": "igw-07c6bc8adfff674bf", "owner_id": "314146329201", "region": "ap-northeast-1", "tags": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-dev-vpc-igw", "Project": "baiker"}, "tags_all": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-dev-vpc-igw", "Project": "baiker"}, "timeouts": null, "vpc_id": "vpc-0ba8398aabd49f856"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_nat_gateway", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"allocation_id": "eipalloc-03dab13d5af1f1660", "association_id": "eipassoc-04ae36e2af9297fba", "connectivity_type": "public", "id": "nat-05152038aac28af1f", "network_interface_id": "eni-00ccb7202d89fd1dd", "private_ip": "*********", "public_ip": "*************", "region": "ap-northeast-1", "secondary_allocation_ids": [], "secondary_private_ip_address_count": 0, "secondary_private_ip_addresses": [], "subnet_id": "subnet-0d451afced7d6f0c0", "tags": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-dev-vpc-nat", "Project": "baiker"}, "tags_all": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-dev-vpc-nat", "Project": "baiker"}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTgwMDAwMDAwMDAwMCwidXBkYXRlIjo2MDAwMDAwMDAwMDB9fQ==", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_subnet.public", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "app-rt", "schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:route-table/rtb-0905b4fe894f1ec7c", "id": "rtb-0905b4fe894f1ec7c", "owner_id": "314146329201", "propagating_vgws": [], "region": "ap-northeast-1", "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "nat-05152038aac28af1f", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-app-sn-rtb", "Project": "baiker"}, "tags_all": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-app-sn-rtb", "Project": "baiker"}, "timeouts": null, "vpc_id": "vpc-0ba8398aabd49f856"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_nat_gateway.main", "aws_subnet.public", "aws_vpc.main"]}, {"index_key": "db-rt", "schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:route-table/rtb-0a5849b6d2df8d478", "id": "rtb-0a5849b6d2df8d478", "owner_id": "314146329201", "propagating_vgws": [], "region": "ap-northeast-1", "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "nat-05152038aac28af1f", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-db-sn-rtb", "Project": "baiker"}, "tags_all": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-db-sn-rtb", "Project": "baiker"}, "timeouts": null, "vpc_id": "vpc-0ba8398aabd49f856"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_nat_gateway.main", "aws_subnet.public", "aws_vpc.main"]}, {"index_key": "web-rt", "schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:route-table/rtb-0b24bc70f06a8c715", "id": "rtb-0b24bc70f06a8c715", "owner_id": "314146329201", "propagating_vgws": [], "region": "ap-northeast-1", "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "nat-05152038aac28af1f", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-web-sn-rtb", "Project": "baiker"}, "tags_all": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-web-sn-rtb", "Project": "baiker"}, "timeouts": null, "vpc_id": "vpc-0ba8398aabd49f856"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_nat_gateway.main", "aws_subnet.public", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:route-table/rtb-02c2726a96806e1b2", "id": "rtb-02c2726a96806e1b2", "owner_id": "314146329201", "propagating_vgws": [], "region": "ap-northeast-1", "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "igw-07c6bc8adfff674bf", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-dev-vpc-public-rt", "Project": "baiker"}, "tags_all": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-dev-vpc-public-rt", "Project": "baiker"}, "timeouts": null, "vpc_id": "vpc-0ba8398aabd49f856"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.main", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "app-private", "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-019f7a8f4bc0d8186", "region": "ap-northeast-1", "route_table_id": "rtb-0905b4fe894f1ec7c", "subnet_id": "subnet-0f9cee2d8eef72cae", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_nat_gateway.main", "aws_route_table.private", "aws_subnet.private", "aws_subnet.public", "aws_vpc.main"]}, {"index_key": "rds-private", "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-076394bad8f13a9e4", "region": "ap-northeast-1", "route_table_id": "rtb-0a5849b6d2df8d478", "subnet_id": "subnet-007c248f2c1c1132c", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_nat_gateway.main", "aws_route_table.private", "aws_subnet.private", "aws_subnet.public", "aws_vpc.main"]}, {"index_key": "redis-private", "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0916a4b149be96b51", "region": "ap-northeast-1", "route_table_id": "rtb-0a5849b6d2df8d478", "subnet_id": "subnet-00cff853458dd9635", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_nat_gateway.main", "aws_route_table.private", "aws_subnet.private", "aws_subnet.public", "aws_vpc.main"]}, {"index_key": "web-private", "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-02ffd861d63dd35c0", "region": "ap-northeast-1", "route_table_id": "rtb-0b24bc70f06a8c715", "subnet_id": "subnet-0305f9862878c29e5", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_nat_gateway.main", "aws_route_table.private", "aws_subnet.private", "aws_subnet.public", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "app-public", "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0686d98a3435dafc5", "region": "ap-northeast-1", "route_table_id": "rtb-02c2726a96806e1b2", "subnet_id": "subnet-0d451afced7d6f0c0", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.main", "aws_route_table.public", "aws_subnet.public", "aws_vpc.main"]}, {"index_key": "web-public", "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-02843552557bc897e", "region": "ap-northeast-1", "route_table_id": "rtb-02c2726a96806e1b2", "subnet_id": "subnet-008286c9b916e4388", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.main", "aws_route_table.public", "aws_subnet.public", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "app-private", "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:subnet/subnet-0f9cee2d8eef72cae", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-northeast-1c", "availability_zone_id": "apne1-az1", "cidr_block": "*********/20", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0f9cee2d8eef72cae", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "314146329201", "private_dns_hostname_type_on_launch": "ip-name", "region": "ap-northeast-1", "tags": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-app-sn", "Project": "baiker", "Type": "Private"}, "tags_all": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-app-sn", "Project": "baiker", "Type": "Private"}, "timeouts": null, "vpc_id": "vpc-0ba8398aabd49f856"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"]}, {"index_key": "rds-private", "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:subnet/subnet-007c248f2c1c1132c", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-northeast-1a", "availability_zone_id": "apne1-az4", "cidr_block": "*********/20", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-007c248f2c1c1132c", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "314146329201", "private_dns_hostname_type_on_launch": "ip-name", "region": "ap-northeast-1", "tags": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-rds-sn", "Project": "baiker", "Type": "Private"}, "tags_all": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-rds-sn", "Project": "baiker", "Type": "Private"}, "timeouts": null, "vpc_id": "vpc-0ba8398aabd49f856"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"]}, {"index_key": "redis-private", "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:subnet/subnet-00cff853458dd9635", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-northeast-1c", "availability_zone_id": "apne1-az1", "cidr_block": "*********/20", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-00cff853458dd9635", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "314146329201", "private_dns_hostname_type_on_launch": "ip-name", "region": "ap-northeast-1", "tags": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-redis-sn", "Project": "baiker", "Type": "Private"}, "tags_all": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-redis-sn", "Project": "baiker", "Type": "Private"}, "timeouts": null, "vpc_id": "vpc-0ba8398aabd49f856"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"]}, {"index_key": "web-private", "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:subnet/subnet-0305f9862878c29e5", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-northeast-1a", "availability_zone_id": "apne1-az4", "cidr_block": "*********/20", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0305f9862878c29e5", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "314146329201", "private_dns_hostname_type_on_launch": "ip-name", "region": "ap-northeast-1", "tags": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-web-sn", "Project": "baiker", "Type": "Private"}, "tags_all": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-web-sn", "Project": "baiker", "Type": "Private"}, "timeouts": null, "vpc_id": "vpc-0ba8398aabd49f856"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "app-public", "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:subnet/subnet-0d451afced7d6f0c0", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-northeast-1a", "availability_zone_id": "apne1-az4", "cidr_block": "10.0.0.0/20", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0d451afced7d6f0c0", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "314146329201", "private_dns_hostname_type_on_launch": "ip-name", "region": "ap-northeast-1", "tags": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-app-pl-sn", "Project": "baiker", "Type": "Public"}, "tags_all": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-app-pl-sn", "Project": "baiker", "Type": "Public"}, "timeouts": null, "vpc_id": "vpc-0ba8398aabd49f856"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"]}, {"index_key": "web-public", "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:subnet/subnet-008286c9b916e4388", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-northeast-1c", "availability_zone_id": "apne1-az1", "cidr_block": "*********/20", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-008286c9b916e4388", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "314146329201", "private_dns_hostname_type_on_launch": "ip-name", "region": "ap-northeast-1", "tags": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-web-pl-sn", "Project": "baiker", "Type": "Public"}, "tags_all": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-web-pl-sn", "Project": "baiker", "Type": "Public"}, "timeouts": null, "vpc_id": "vpc-0ba8398aabd49f856"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_vpc", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:314146329201:vpc/vpc-0ba8398aabd49f856", "assign_generated_ipv6_cidr_block": false, "cidr_block": "10.0.0.0/16", "default_network_acl_id": "acl-0a55ac3a764cf7535", "default_route_table_id": "rtb-0b45f23c8032c7203", "default_security_group_id": "sg-0756b3ac5c1dc92e3", "dhcp_options_id": "dopt-0ced1e466dc80e32d", "enable_dns_hostnames": true, "enable_dns_support": true, "enable_network_address_usage_metrics": false, "id": "vpc-0ba8398aabd49f856", "instance_tenancy": "default", "ipv4_ipam_pool_id": null, "ipv4_netmask_length": null, "ipv6_association_id": "", "ipv6_cidr_block": "", "ipv6_cidr_block_network_border_group": "", "ipv6_ipam_pool_id": "", "ipv6_netmask_length": 0, "main_route_table_id": "rtb-0b45f23c8032c7203", "owner_id": "314146329201", "region": "ap-northeast-1", "tags": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-dev-vpc", "Project": "baiker"}, "tags_all": {"Environment": "dev", "ManagedBy": "terraform", "Name": "baiker-dev-vpc", "Project": "baiker"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ=="}]}], "check_results": null}