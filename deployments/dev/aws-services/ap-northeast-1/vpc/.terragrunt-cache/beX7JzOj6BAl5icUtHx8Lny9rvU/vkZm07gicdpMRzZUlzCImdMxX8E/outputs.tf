output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.main.id
}

output "vpc_arn" {
  description = "ARN of the VPC"
  value       = aws_vpc.main.arn
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = aws_vpc.main.cidr_block
}

output "internet_gateway_id" {
  description = "ID of the Internet Gateway"
  value       = aws_internet_gateway.main.id
}

output "public_subnets" {
  description = "Map of public subnets with their details"
  value = {
    for key, subnet in aws_subnet.public : key => {
      id                = subnet.id
      arn               = subnet.arn
      cidr_block        = subnet.cidr_block
      availability_zone = subnet.availability_zone
    }
  }
}

output "private_subnets" {
  description = "Map of private subnets with their details"
  value = {
    for key, subnet in aws_subnet.private : key => {
      id                = subnet.id
      arn               = subnet.arn
      cidr_block        = subnet.cidr_block
      availability_zone = subnet.availability_zone
    }
  }
}

output "public_subnet_ids" {
  description = "List of IDs of the public subnets"
  value       = [for subnet in aws_subnet.public : subnet.id]
}

output "private_subnet_ids" {
  description = "List of IDs of the private subnets"
  value       = [for subnet in aws_subnet.private : subnet.id]
}

output "public_subnet_cidrs" {
  description = "List of CIDR blocks of the public subnets"
  value       = [for subnet in aws_subnet.public : subnet.cidr_block]
}

output "private_subnet_cidrs" {
  description = "List of CIDR blocks of the private subnets"
  value       = [for subnet in aws_subnet.private : subnet.cidr_block]
}

output "nat_gateway_ids" {
  description = "List of IDs of the NAT Gateways"
  value       = aws_nat_gateway.main[*].id
}

output "nat_gateway_public_ips" {
  description = "List of public Elastic IPs for NAT Gateway"
  value       = aws_eip.nat[*].public_ip
}

output "public_route_table_id" {
  description = "ID of the public route table"
  value       = aws_route_table.public.id
}

output "private_route_tables" {
  description = "Map of private route tables with their details"
  value = {
    for key, rt in aws_route_table.private : key => {
      id  = rt.id
      arn = rt.arn
    }
  }
}

output "private_route_table_ids" {
  description = "List of IDs of the private route tables"
  value       = [for rt in aws_route_table.private : rt.id]
}

output "default_network_acl_id" {
  description = "ID of the default Network ACL"
  value       = aws_default_network_acl.default.id
}

output "default_route_table_id" {
  description = "ID of the default route table"
  value       = aws_default_route_table.default.id
}

output "vpc_endpoint_ids" {
  description = "Map of VPC endpoint IDs"
  value = {
    for key, endpoint in aws_vpc_endpoint.interface_endpoints : key => endpoint.id
  }
}