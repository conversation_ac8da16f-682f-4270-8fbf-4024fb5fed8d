# Baiker Infrastructure as Code (IaC)

Terraform modules for provisioning AWS infrastructure for the Baiker development environment.

## 🏗️ Architecture Overview

This IaC project creates a complete AWS infrastructure including:

- **Networking**: VPC with public/private subnets, NAT Gateway, Internet Gateway
- **Compute**: EC2 instances (Bastion + Main), ECR repository
- **Load Balancing**: Network Load Balancer with multiple target groups
- **Database**: RDS PostgreSQL with encryption and monitoring
- **Caching**: MemoryDB Redis cluster
- **Security**: Security groups, Cognito authentication, IAM roles
- **Storage**: S3 buckets for state and data

## 📋 Prerequisites

Before deploying, ensure you have:

1. **AWS CLI configured** with appropriate permissions
2. **Terraform >= 1.5** installed
3. **Required AWS permissions** for all services used (including Secrets Manager)

**No SSH key generation required!** The infrastructure automatically generates AWS-managed ED25519 key pairs and stores private keys securely in AWS Secrets Manager.

## 🚀 Quick Start

### 1. Initialize Terraform
```bash
cd terraform
terraform init
```

### 2. Review and Customize Variables
```bash
# Copy example variables (optional)
cp terraform.tfvars.example terraform.tfvars

# Edit variables as needed
vim terraform.tfvars
```

### 3. Plan Deployment
```bash
terraform plan
```

### 4. Deploy Infrastructure
```bash
terraform apply
```

### 5. Verify Deployment
```bash
# Retrieve SSH private key from AWS Secrets Manager
SECRET_ARN=$(terraform output -raw private_key_secret_arn)
aws secretsmanager get-secret-value --secret-id $SECRET_ARN \
  --query SecretString --output text | \
  jq -r '.private_key_openssh' > ~/.ssh/baiker-dev-key.pem
chmod 600 ~/.ssh/baiker-dev-key.pem

# SSH to bastion host
ssh -i ~/.ssh/baiker-dev-key.pem ubuntu@$(terraform output -raw bastion_eip)

# Check database connectivity (from within VPC)
psql "$(terraform output -raw rds_connection_string)" -c "SELECT version();"
```

## 📂 Project Structure

```
.
├── README.md
├── terraform/
│   ├── main.tf              # Main configuration
│   ├── variables.tf         # Input variables
│   ├── outputs.tf          # Output values
│   └── modules/            # Terraform modules
│       ├── networking/
│       │   ├── vpc/        # VPC, subnets, gateways
│       │   └── load-balancer/ # NLB, target groups
│       ├── compute/
│       │   ├── ec2/        # EC2 instances, key pairs
│       │   └── ecr/        # Container registry
│       ├── database/
│       │   ├── rds/        # PostgreSQL database
│       │   └── redis/      # MemoryDB Redis
│       ├── security/
│       │   ├── security-groups/ # Security groups
│       │   └── cognito/    # User authentication
│       └── storage/
│           └── s3/         # S3 buckets
└── deployments/           # Terragrunt configurations (future)
```

## 🔧 Configuration

### Key Variables

| Variable | Description | Default |
|----------|-------------|---------||
| `aws_region` | AWS region | `ap-northeast-1` |
| `project_name` | Project name | `baiker` |
| `environment` | Environment | `dev` |
| `vpc_cidr` | VPC CIDR block | `10.0.0.0/16` |
| `allowed_ssh_cidr` | SSH access CIDR | `*************/32` |
| `ami_id` | EC2 AMI ID | `ami-054400ced365b82a0` |
| `db_name` | Database name | `baiker_dev` |
| `db_port` | Database port | `5454` |

### Terraform Variables File

Create `terraform/terraform.tfvars`:

```hcl
aws_region = "ap-northeast-1"
project_name = "baiker"
environment = "dev"

# Network Configuration
vpc_cidr = "10.0.0.0/16"
availability_zones = ["ap-northeast-1a", "ap-northeast-1b"]

# Security
allowed_ssh_cidr = "YOUR_IP_ADDRESS/32"  # Replace with your IP

# Instance Configuration
ami_id = "ami-054400ced365b82a0"  # Ubuntu 24.04 LTS
bastion_instance_type = "t2.micro"
main_instance_type = "t2.medium"

# Database Configuration
db_name = "baiker_dev"
db_username = "baiker"
db_port = 5454
rds_instance_class = "db.m7g.large"

# Tags
default_tags = {
  Project     = "baiker"
  Environment = "dev"
  Owner       = "your-name"
  ManagedBy   = "terraform"
}
```

## 🧪 Testing Each Module

Test individual modules before full deployment:

### VPC Module
```bash
cd terraform/modules/networking/vpc
terraform init
terraform plan -var="vpc_name=test-vpc"
terraform apply -var="vpc_name=test-vpc"
```

### Security Groups Module
```bash
cd terraform/modules/security/security-groups
terraform init
terraform plan -var="vpc_id=vpc-xxxxxxxxx"
```

### EC2 Module
```bash
cd terraform/modules/compute/ec2
terraform init
# Ensure SSH key exists first
ls -la ~/.ssh/id_ed25519.pub
terraform plan
```

### RDS Module
```bash
cd terraform/modules/database/rds
terraform init
terraform plan
# Check created database
aws rds describe-db-instances --db-instance-identifier baiker-dev-rds-postgre
```

## 🔍 Verification Commands

### Infrastructure Verification
```bash
# Check all resources
terraform state list

# Get outputs
terraform output

# Verify VPC
aws ec2 describe-vpcs --vpc-ids $(terraform output -raw vpc_id)

# Verify EC2 instances
aws ec2 describe-instances --instance-ids $(terraform output -raw bastion_instance_id) $(terraform output -raw main_instance_id)

# Verify Load Balancer
aws elbv2 describe-load-balancers --load-balancer-arns $(terraform output -raw nlb_arn)

# Verify RDS
aws rds describe-db-instances --db-instance-identifier baiker-dev-rds-postgre

# Verify ECR
aws ecr describe-repositories --repository-names entanglement
```

### Connectivity Testing
```bash
# First, retrieve SSH private key
SECRET_ARN=$(terraform output -raw private_key_secret_arn)
aws secretsmanager get-secret-value --secret-id $SECRET_ARN \
  --query SecretString --output text | \
  jq -r '.private_key_openssh' > ~/.ssh/baiker-dev-key.pem
chmod 600 ~/.ssh/baiker-dev-key.pem

# SSH to bastion
ssh -i ~/.ssh/baiker-dev-key.pem ubuntu@$(terraform output -raw bastion_eip)

# SSH to main instance through bastion
ssh -i ~/.ssh/baiker-dev-key.pem -J ubuntu@$(terraform output -raw bastion_eip) ubuntu@$(terraform output -raw main_instance_private_ip)

# Test database (from within VPC) - get password from Secrets Manager
DB_SECRET=$(terraform output -raw rds_secret_arn)
DB_PASSWORD=$(aws secretsmanager get-secret-value --secret-id $DB_SECRET --query SecretString --output text | jq -r '.password')
psql "******************************************** output -raw rds_endpoint):5454/baiker_dev"

# Test Redis (from within VPC)
redis-cli -h $(terraform output -raw redis_cluster_endpoint) -p 6369
```

## 🔐 Security Features

- **AWS-Managed SSH Keys**: ED25519 keys generated and stored in Secrets Manager
- **No Local Key Exposure**: Private keys never stored on development machines
- **Network Security**: Private subnets for databases, security groups
- **Encryption**: KMS encryption for RDS, AES256 for S3 and ECR
- **Access Control**: Bastion host for SSH, security group restrictions
- **Secrets Management**: Auto-generated passwords and keys in AWS Secrets Manager
- **IAM Permissions**: Controlled access to retrieve keys and credentials
- **Monitoring**: CloudWatch logs, Performance Insights, Enhanced Monitoring

## 💰 Cost Optimization

- **Development Environment**: Single-AZ deployments, smaller instance types
- **Storage**: GP3 volumes for better price/performance
- **Compute**: t2.micro bastion (free tier eligible), t2.medium main instance
- **NAT**: Single NAT Gateway (cost vs availability trade-off)

## 🚨 Troubleshooting

### Common Issues

1. **Cannot Access SSH Key**
   ```bash
   # Verify you have access to retrieve secrets
   aws secretsmanager get-secret-value --secret-id $(terraform output -raw private_key_secret_arn)
   
   # Check IAM permissions for secretsmanager:GetSecretValue
   aws iam get-user
   ```

2. **Permission Denied**
   ```bash
   # Check AWS credentials
   aws sts get-caller-identity
   ```

3. **Resource Already Exists**
   ```bash
   # Import existing resource
   terraform import aws_s3_bucket.example bucket-name
   ```

4. **Database Connection Issues**
   ```bash
   # Get connection details from Secrets Manager
   aws secretsmanager get-secret-value --secret-id $(terraform output -raw rds_secret_arn)
   ```

### Debug Commands
```bash
# Enable detailed logging
export TF_LOG=DEBUG
terraform apply

# Check resource state
terraform show

# Validate configuration
terraform validate

# Format code
terraform fmt -recursive
```

## 🧹 Cleanup

### Destroy Infrastructure
```bash
# Destroy all resources
terraform destroy

# Confirm destruction
aws ec2 describe-instances --query 'Reservations[].Instances[?Tags[?Key==`Project`]|[?Value==`baiker`]].[InstanceId,State.Name]'
```

### Manual Cleanup (if needed)
```bash
# Remove any remaining resources
# Check for resources with 'baiker' tag
aws resourcegroupstaggingapi get-resources --tag-filters Key=Project,Values=baiker
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes and test
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Review AWS documentation
3. Create an issue in this repository

---

**Note**: This infrastructure is configured for development environments. For production use, consider enabling Multi-AZ deployments, increasing backup retention, enabling deletion protection, and implementing additional security measures.